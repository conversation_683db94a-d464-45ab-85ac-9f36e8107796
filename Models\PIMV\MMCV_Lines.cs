﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class MMCV_Lines
    {
        public static DataTable GET_Lines(string AreaName, out string exception)
        {
            string query = "Select * From MMCV_Lines Where [AreaName] = @AreaName ";
            return SERVER_PV.Instance.ExcuteQuery(out exception,SERVER_DF.Instance.SV68_PIMV, query, new object[] { AreaName });
        }

        public static DataTable GET_Lines(int AreaID, out string exception)
        {
            string query = "Select * From MMCV_Lines Where [AreaID] = @AreaID ";
            return SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV68_PIMV, query, new object[] { AreaID });
        }
    }
}
