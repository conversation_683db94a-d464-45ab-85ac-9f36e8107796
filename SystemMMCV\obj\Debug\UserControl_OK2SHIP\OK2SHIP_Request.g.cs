﻿#pragma checksum "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "179FF071916D24F6C3A82AC396777B061982ACBE2805534D0BFD4A8D52047400"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_OK2SHIP;


namespace SystemMMCV.UserControl_OK2SHIP {
    
    
    /// <summary>
    /// OK2SHIP_Request
    /// </summary>
    public partial class OK2SHIP_Request : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 10 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas body;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel wpn_ContentHearder;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_model;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_program;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_build;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_config;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_requestno;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker txt_DatePro;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_Shift;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_line;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_lotno;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_mpn;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ckb_keep;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer scv_autocontent;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid grd_AutoItem;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_ok2ship/ok2ship_request.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.body = ((System.Windows.Controls.Canvas)(target));
            return;
            case 2:
            this.wpn_ContentHearder = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 3:
            this.txt_model = ((System.Windows.Controls.ComboBox)(target));
            
            #line 36 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
            this.txt_model.DropDownClosed += new System.EventHandler(this.Event_cbb_ModelName_Change);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txt_program = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.txt_build = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.txt_config = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.txt_requestno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txt_DatePro = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.txt_Shift = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.txt_line = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.txt_lotno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.txt_mpn = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            
            #line 68 "..\..\..\UserControl_OK2SHIP\OK2SHIP_Request.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_SendRequest);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ckb_keep = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.scv_autocontent = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 16:
            this.grd_AutoItem = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

