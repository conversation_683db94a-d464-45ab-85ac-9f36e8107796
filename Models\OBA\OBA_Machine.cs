﻿using System;

namespace Models.OBA
{
    public class OBA_Machine
    {
        private int serialID;
        private string machine;
        private DateTime? calibration;
        private DateTime? maintenance;
        private string staffNo;
        private DateTime? enterTime;

        public int SerialID { get => serialID; set => serialID = value; }
        public string Machine { get => machine; set => machine = value; }
        public DateTime? Calibration { get => calibration; set => calibration = value; }
        public DateTime? Maintenance { get => maintenance; set => maintenance = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }
    }
}
