﻿using System.Collections.Generic;
using System.Data;

namespace Models
{
    public class Func<PERSON>rovider
    {
        //private static FuncProvider instance;

        //public static FuncProvider Instance { get { if (instance == null) instance = new FuncProvider(); return instance; } set => instance = value; }
        public List<MMCV_Function> FunctionMMCV()
        {
            var functionMMCV = new List<MMCV_Function>();
            string query = "Select Top(1000) * From [MMCV_Function] With(Nolock) order by [FunctionName]";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            if (data != null && data.Rows.Count > 0)
            {
                functionMMCV = new List<MMCV_Function>();
                foreach (DataRow item in data.Rows)
                {
                    functionMMCV.Add(new MMCV_Function(item));
                }
            }

            return functionMMCV;

        }

        public bool Add_Function(MMCV_Function func)
        {
            string query = "Insert Into [MMCV_Function] ([FunctionID],[FunctionName],[GroupName],[Describe],[UserCreate],[EnterTime]) Values ( @FunctionID , @FunctionName , @GroupName , @Describe , @UserCreate , @EnterTime )";
            var parameter = new object[] { func.FunctionID, func.FunctionName, func.GroupName, func.Describe, func.UserCreate, func.EnterTime };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Edit_Function(MMCV_Function func)
        {
            string query = "Update [MMCV_Function] Set [FunctionName] = @FunctionName , [GroupName] = @GroupName ,[Describe] = @Describe ,[UserCreate] = @UserCreate ,[EnterTime] = @EnterTime Where [FunctionID] = @FunctionID ";
            var parameter = new object[] { func.FunctionName, func.GroupName, func.Describe, func.UserCreate, func.EnterTime, func.FunctionID };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Delete_Function(MMCV_Function func)
        {
            string query = "Delete From [MMCV_Function] Where [FunctionID] = @FunctionID ";
            var parameter = new object[] { func.FunctionID };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

    }
}
