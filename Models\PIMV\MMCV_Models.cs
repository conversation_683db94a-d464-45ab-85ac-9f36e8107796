﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class MMCV_Models_EXC
    {
        public static List<string> Get_Models(out string exception)
        {
            string query = "Select Distinct(Model) From MMCV_Models";
            var data = SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV68_PIMV, query);
            if (string.IsNullOrEmpty(exception))
            {
                if (data != null && data.Rows.Count > 0)
                {
                    return data.AsEnumerable()
                        .Select(row => row.Field<string>("Model"))
                        .ToList();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }
    }
}
