﻿
using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;


namespace Models.JIG
{
    public class JIG_Provider
    {


        public string Get_JigName(string tooljigId)
        {
            string query = $"Select [Jig_Name] From [ToolJig_TimesPunch] Where [SerialID] = {tooljigId}";
            var res = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server48_PIMV, query);
            return res.ToString();
        }
        public DataTable ToolJig_Table(string jigName = null, bool like = true)
        {
            string query = "";
            if (string.IsNullOrEmpty(jigName))
            {
                query = "Select Top(100) a.[SerialID],[Area],[Line],[Model],[ProcessID],b.[DeviceID],[Jig_Name],[Jig_Type],[ShtBarcode],[Type]," +
                    "[PartNumber],[Jig_Punch],[Jig_Limit],[WarningPunch],[StartedDate],[NumDaysUse],[WarningDay],[NumResets],[TotalResets],[OnlyOnce],[Status],[StaffNo],[LastTime],[ReportLink] " +
                    "From[ToolJig_TimesPunch] as a  With(Nolock) left join ToolJig_Devices as b On a.Jig_Name = b.ToolJigID Order By a.SerialID";
            }
            else
            {
                if (like)
                    query = "Select a.[SerialID],[Area],[Line],[Model],[ProcessID],b.[DeviceID],[Jig_Name],[Jig_Type],[ShtBarcode],[Type],[PartNumber],[Jig_Punch],[Jig_Limit]," +
                        "[WarningPunch],[StartedDate],[NumDaysUse],[WarningDay] ,[NumResets],[TotalResets],[OnlyOnce],[Status],[StaffNo],[LastTime],[ReportLink]  " +
                        "From ToolJig_TimesPunch as a  With(Nolock) left join ToolJig_Devices as b On a.Jig_Name = b.ToolJigID " +
                        "Where Jig_Name like '" + jigName + "%' Order By a.SerialID";
                else
                    query = "Select a.[SerialID],[Area],[Line],[Model],[ProcessID],b.[DeviceID],[Jig_Name],[Jig_Type],[ShtBarcode],[Type],[PartNumber],[Jig_Punch],[Jig_Limit]," +
                    "[WarningPunch],[StartedDate],[NumDaysUse],[WarningDay] ,[NumResets],[TotalResets],[OnlyOnce],[Status],[StaffNo],[LastTime],[ReportLink]  " +
                    "From ToolJig_TimesPunch as a  With(Nolock) left join ToolJig_Devices as b On a.Jig_Name = b.ToolJigID " +
                    "Where Jig_Name = '" + jigName + "' Order By a.SerialID";
            }

            if (query != "")
            {
                var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server48_PIMV, query);
                return data;
            }
            else
            {
                return null;
            }
        }

        public bool ToolJig_Reset(TimesPunch myItem, string DesFilePath, string Username)
        {
            var total = myItem.TotalResets + myItem.Jig_Punch;
            var timesReset = myItem.NumResets + 1;
            var parameter = new object[] { 0, timesReset, total, myItem.Jig_Name, myItem.SerialID, timesReset, myItem.Jig_Punch, DesFilePath, Username };
            string query = "Update [ToolJig_TimesPunch] Set [Jig_Punch] = @Pucnch , [NumResets] = @NumResets , [TotalResets] = @TotalReset Where [Jig_Name] = @JigName ;"
            + "Insert Into ToolJig_Details ([ToolJigID],[TimesReset],[Jig_Punch],[Evidence],[StaffNo]) Values ( @ToolJigID , @TimesReset , @Jig_Punch , @Evidence , @StaffNo );";
            var res = DataProvider.Instance.ExcuteNonTransaction(DataProvider.MMCV_Server.Server48_PIMV, query, parameter);
            return res > 0;
        }
        public bool TimesPunch_Edit(out string exception, TimesPunch item)
        {
            //Chuyển dữ liệu SMT
            string query_history = $"Select [Area] from [ToolJig_TimesPunch] where Jig_Name = '{item.Jig_Name}'";
            var data_history = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC48_PIMV, query_history);
            if (string.IsNullOrEmpty(exception))
            {
                if (data_history != null && data_history.Rows.Count == 1)
                {
                    var _AreaID = ModelsProvider.SqlInstance.ExecuteScalar(out exception, SqlProvider.SqlSV.OFC68_PIMV, $"SELECT [AreaID] FROM[PIMV].[dbo].[MMCV_Areas] Where AreaName = '{item.Area}'");
                    if (!string.IsNullOrEmpty(exception)) return false;
                    var _ModelID = ModelsProvider.SqlInstance.ExecuteScalar(out exception, SqlProvider.SqlSV.OFC68_PIMV, $"SELECT [SerialID] FROM [PIMV].[dbo].[MMCV_Models] Where Model = '{item.Model}'");
                    if (!string.IsNullOrEmpty(exception)) return false;
                    //if (_AreaID == DBNull.Value) _AreaID = 0;
                    //if (_ModelID == DBNull.Value) _ModelID = 0;
                    if (_AreaID != null && _ModelID != null)
                    {
                        string query_delete = $"Delete FROM [Record].[dbo].[BoardManage] where Barcode = '{item.Jig_Name}';";
                        var server48 = SqlProvider.SqlSV.OFC48_PIMV;
                        var server68 = SqlProvider.SqlSV.OFC68_PIMV;
                        SqlProvider.SqlSV? server_source = null;
                        SqlProvider.SqlSV? server_destin = null;
                        StringBuilder buider_source = new StringBuilder();
                        StringBuilder buider_destin = new StringBuilder();
                        StringBuilder buider_48PIMV = new StringBuilder();
                        StringBuilder buider_68PIMV = new StringBuilder();
                        buider_source.AppendLine(query_delete);
                        buider_destin.AppendLine(query_delete);

                        var row_history = data_history.Rows[0];
                        var area_history = row_history["Area"].ToString();
                        if (area_history != item.Area)
                        {
                            var _BoardTypes = ConvertBoardTypes(item.Jig_Type);
                            if (_BoardTypes != null)
                            {
                                //Nếu cần chuyển sang SMT-AB hoặc SMT-C
                                if (item.Area == "SMT-AB" || item.Area == "SMT-C")
                                {
                                    switch (area_history)
                                    {
                                        case "SMT-AB":
                                            server_source = SqlProvider.SqlSV.OFC46_Record;
                                            server_destin = SqlProvider.SqlSV.OFC69_Record;
                                            break;
                                        case "SMT-C":
                                            server_source = SqlProvider.SqlSV.OFC69_Record;
                                            server_destin = SqlProvider.SqlSV.OFC46_Record;
                                            break;
                                        default:
                                            server_source = null;
                                            server_destin = null;
                                            break;
                                    }
                                    //Kiểm tra xem có cần xử lý chuyển server không
                                    if (server_source != null && server_destin != null)
                                    {
                                        string query_record = $"Select * from [BoardManage] where Barcode = '{item.Jig_Name}';";
                                        var data_record = ModelsProvider.SqlInstance.ExecuteQuery(out exception, (SqlProvider.SqlSV)server_source, query_record);
                                        if (string.IsNullOrEmpty(exception))
                                        {
                                            if (data_record != null && data_record.Rows.Count > 0)
                                            {
                                                var row_record = data_record.Rows[0];
                                                string _Barcode = row_record["Barcode"].ToString();
                                                string _MaintainFrequency = row_record["MaintainFrequency"].ToString();
                                                string _AfterMaintainUseTimes = row_record["AfterMaintainUseTimes"].ToString();
                                                string _ScrapLimitTimes = row_record["ScrapLimitTimes"].ToString();
                                                string _HistoryUseTimes = row_record["HistoryUseTimes"].ToString();
                                                string _State = row_record["State"].ToString();
                                                string _Directory = row_record["Directory"].ToString();
                                                //string _Line = row_record["Line"].ToString();
                                                string _Line = item.Line;

                                                string query_insert = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line],[Entertime]) Values ('{_Barcode}','{_BoardTypes}','{_MaintainFrequency}','{_AfterMaintainUseTimes}','{_ScrapLimitTimes}','{_HistoryUseTimes}','{_State}','{_Directory}','{_Line}', GetDate());";
                                                buider_destin.AppendLine(query_insert);
                                            }
                                            else
                                            {
                                                string query_insert = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line],[Entertime]) Values ('{item.Jig_Name}','0',GETDATE(),0,0,{item.Jig_Limit},0,0,'','{item.Line}', GetDate());";
                                                buider_destin.AppendLine(query_insert);
                                            }
                                        }
                                        else
                                        {
                                            return false;
                                        }
                                    }
                                }
                            }
                        }

                        //Edit Jig_manager
                        string query_update = "";
                        switch (item.Type)
                        {
                            case "Times Punch":
                                query_update = $"Update [ToolJig_TimesPunch] Set [Area] = '{item.Area}' ,[Line] = '{item.Line}' ,[Model] = '{item.Model}' , [ProcessID] = '{item.ProcessID}' ,[Type] = '{item.Type}' ,[Jig_Type] = '{item.Jig_Type}' ,[PartNumber] = '{item.PartNumber}' , [WarningPunch] = '{item.WarningPunch}' , [Jig_Limit] = '{item.Jig_Limit}' , [OnlyOnce] = '{item.OnlyOnce}' ,[ShtBarcode] = '{item.ShtBarcode}' ,[StaffNo] = '{item.StaffNo}' , [ReportLink] = N'{item.ReportLink}' Where [Jig_Name] = '{item.Jig_Name}';";
                                break;
                            case "Used Time":
                                query_update = $"Update [ToolJig_TimesPunch] Set [Area] = '{item.Area}' ,[Line] = '{item.Line}' ,[Model] = '{item.Model}' ,[ProcessID] = '{item.ProcessID}' ,[Type] = '{item.Type}' ,[Jig_Type] = '{item.Jig_Type}' , [PartNumber] = '{item.PartNumber}' , [WarningPunch] = '{item.WarningPunch}' , [WarningDay] = '{item.WarningDay}' , [Jig_Limit] = '{item.Jig_Limit}' , [NumDaysUse] = '{item.NumDaysUse}'  , [OnlyOnce] = '{item.OnlyOnce}' ,[ShtBarcode] = '{item.ShtBarcode}' ,[StaffNo] = '{item.StaffNo}' , [ReportLink] = '{item.ReportLink}' Where [Jig_Name] = '{item.Jig_Name}';";
                                break;
                            default:
                                exception = "Kiểu quản lý không hợp lệ.";
                                return false;
                        }

                        buider_48PIMV.AppendLine(query_update);
                        string query_devices = $"Update [ToolJig_Devices] Set DeviceID = CONCAT(LEFT(DeviceID, LEN(DeviceID) -6), '{item.Line}', RIGHT(DeviceID, 2)) Where ToolJigID = '{item.Jig_Name}';";
                        buider_48PIMV.AppendLine(query_devices);
                        string query_quanlification = $"Update [MQ_ToolJigQualification] Set MachineID = CONCAT(LEFT([MachineID], LEN([MachineID]) - 6), '{item.Line}', RIGHT([MachineID], 2)), LineID = '{item.Line}', ProcessID = '{item.ProcessID}', ReportLink = '{item.ReportLink}', [AreaID] = '{_AreaID}', ProductModelID = '{_ModelID}', [DateUpdate] = FORMAT(GETDATE(), 'yyyyMMdd') Where ToolJigID = '{item.Jig_Name}';";
                        buider_68PIMV.AppendLine(query_quanlification);

                        using (SqlConnection conn_48 = new SqlConnection(ModelsProvider.SqlInstance.ConnectString(server48)))
                        {
                            using (SqlConnection conn_68 = new SqlConnection(ModelsProvider.SqlInstance.ConnectString(server68)))
                            {
                                var check_source = (!string.IsNullOrEmpty(buider_source.ToString()) && server_source != null);
                                var check_destin = (!string.IsNullOrEmpty(buider_destin.ToString()) && server_destin != null);
                                using (SqlConnection conn_source = check_source ? new SqlConnection(ModelsProvider.SqlInstance.ConnectString((SqlProvider.SqlSV)server_source)) : null)
                                {
                                    using (SqlConnection conn_destin = check_destin ? new SqlConnection(ModelsProvider.SqlInstance.ConnectString((SqlProvider.SqlSV)server_destin)) : null)
                                    {
                                        conn_48.Open();
                                        conn_68.Open();
                                        conn_source?.Open();
                                        conn_destin?.Open();

                                        SqlTransaction tran_48 = conn_48.BeginTransaction();
                                        SqlTransaction tran_68 = conn_68.BeginTransaction();
                                        SqlTransaction tran_source = conn_source?.BeginTransaction();
                                        SqlTransaction tran_destin = conn_destin?.BeginTransaction();

                                        SqlCommand cmd_48 = new SqlCommand(buider_48PIMV.ToString(), conn_48, tran_48);
                                        SqlCommand cmd_68 = new SqlCommand(buider_68PIMV.ToString(), conn_68, tran_68);
                                        SqlCommand cmd_source = tran_source != null ? new SqlCommand(buider_source.ToString(), conn_source, tran_source) : null;
                                        SqlCommand cmd_destin = tran_destin != null ? new SqlCommand(buider_destin.ToString(), conn_destin, tran_destin) : null;

                                        try
                                        {
                                            cmd_48.ExecuteNonQuery();
                                            cmd_68.ExecuteNonQuery();
                                            cmd_source?.ExecuteNonQuery();
                                            cmd_destin?.ExecuteNonQuery();

                                            tran_48.Commit();
                                            tran_68.Commit();
                                            tran_source?.Commit();
                                            tran_destin?.Commit();

                                            return true;
                                        }
                                        catch (Exception ex)
                                        {
                                            tran_48.Rollback();
                                            tran_68.Rollback();
                                            tran_source?.Rollback();
                                            tran_destin?.Rollback();
                                            exception = ex.Message;
                                            return false;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        exception = "Không tìm thấy thông tin của Area và Model trên master {MMCV_Area - MMCV_Model}.";
                        return false;
                    }
                }
                else
                {
                    exception = "Không tìm thấy bản ghi của Jig trên ToolJig_TimesPunch.";
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        int? ConvertBoardTypes(string type)
        {
            switch (type)
            {
                case "Stencil":
                    return 0;
                case "Jig plate":
                    return 1;
                case "Cover jig":
                    return 2;
                case "Support Block":
                    return 3;
                default:
                    return null;
            }
        }
        public bool TimesPunch_Add(TimesPunch item, out string ex)
        {
            ex = "";
            try
            {
                // 0-Available Default
                // 1-Scarp
                // Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line]) Values ('','',GETDATE(),0,0,'',0,0,'','');
                //Xử lý dữ liệu trong [BroadManage] của MAT
                if (item.Area.Contains("SMT") && item.Type == "Times Punch")
                {
                    string queryMAT = "";
                    switch (item.Jig_Type)
                    {
                        case "Stencil": //0-36000 
                            queryMAT = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line]) Values ('{item.Jig_Name}','0',GETDATE(),0,0,{item.Jig_Limit},0,0,'','{item.Line}');";
                            break;
                        case "Jig plate": //1-1200
                            queryMAT = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line]) Values ('{item.Jig_Name}','1',GETDATE(),0,0,{item.Jig_Limit},0,0,'','{item.Line}');";
                            break;
                        case "Cover jig": //2-1200
                            queryMAT = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line]) Values ('{item.Jig_Name}','2',GETDATE(),0,0,{item.Jig_Limit},0,0,'','{item.Line}');";
                            break;
                        case "Support Block": //3-???
                            queryMAT = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line]) Values ('{item.Jig_Name}','3',GETDATE(),0,0,{item.Jig_Limit},0,0,'','{item.Line}');";
                            break;
                        default:
                            break;
                    }
                    if (!string.IsNullOrEmpty(queryMAT))
                    {
                        var myServer = Get_ServerSMT(item.Area);
                        if (myServer != DataProvider.MMCV_Server.NULL)
                        {
                            string queryCheck = $"Select Count(*) From [BoardManage] Where [Barcode] = '{item.Jig_Name}'";
                            int resCheck = (int)DataProvider.Instance.ExecuteScalar(myServer, queryCheck);
                            if (resCheck > 0)
                            {
                                ex += "JigBarcode đã tồn tại trong [BoardManage]. Không thể ghi.";
                            }
                            else
                            {
                                var resMAT = DataProvider.Instance.ExecuteNonQuery(myServer, queryMAT) > 0;
                                if (resMAT)
                                {
                                    ex += "Cập nhật thành công dữ liệu trong BoardManage" + Environment.NewLine;
                                }
                                else
                                {
                                    ex += "Không ghi được dữ liệu vào BoardManage. Hãy liên lạc với IT: <EMAIL>" + Environment.NewLine;
                                }
                            }
                        }

                    }
                }
                //Xử lý dữ liệu trong ToolJig_Manager
                string query = "";
                object[] parameter = null;
                switch (item.Type)
                {
                    case "Times Punch":
                        query = "Insert Into [ToolJig_TimesPunch] ([Area],[Line],[Model],[ProcessID],[Type],[Jig_Name],[Jig_Type],[ShtBarcode],[PartNumber],[Jig_Punch],[WarningPunch],[Jig_Limit],[NumResets],[TotalResets],[OnlyOnce],[Status],[StaffNo],[ReportLink]) " +
                            "Values ( @Area , @Line , @Model , @ProcessID , @Type , @Jig_Name , @Jig_Type , @ShtBarcode , @PartNumber , @Jig_Punch , @WarningPunch , @Jig_Limit , @NumResets , @TotalResets , @OnlyOnce , @Status , @StaffNo , @ReportLink )";
                        parameter = new object[] { item.Area, item.Line, item.Model, item.ProcessID, item.Type, item.Jig_Name, item.Jig_Type, item.ShtBarcode, item.PartNumber, item.Jig_Punch, item.WarningPunch, item.Jig_Limit, item.NumResets, item.TotalResets, item.OnlyOnce, item.Status, item.StaffNo, item.ReportLink.ToString() };
                        break;
                    case "Used Time":
                        query = "Insert Into [ToolJig_TimesPunch] ([Area],[Line],[Model],[ProcessID],[Type],[Jig_Name],[Jig_Type],[ShtBarcode],[PartNumber],[Jig_Punch],[WarningPunch],[Jig_Limit],[StartedDate],[NumDaysUse],[WarningDay],[NumResets],[TotalResets],[OnlyOnce],[Status],[StaffNo],[ReportLink]) " +
                           "Values ( @Area , @Line , @Model , @ProcessID , @Type , @Jig_Name , @Jig_Type , @ShtBarcode , @PartNumber , @Jig_Punch , @WarningPunch , @Jig_Limit , @StartedDate , @NumDaysUse , @WarningDay , @NumResets , @TotalResets , @OnlyOnce , @Status , @StaffNo , @ReportLink )";
                        parameter = new object[] { item.Area, item.Line, item.Model, item.ProcessID, item.Type, item.Jig_Name, item.Jig_Type, item.ShtBarcode, item.PartNumber, item.Jig_Punch, item.WarningPunch, item.Jig_Limit, item.StartedDate, item.NumDaysUse, item.WarningDay, item.NumResets, item.TotalResets, item.OnlyOnce, item.Status, item.StaffNo, item.ReportLink.ToString() };
                        break;
                    default:
                        ex += "Không đúng kiểu (Types)" + Environment.NewLine;
                        return false;
                }
                var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server48_PIMV, query, parameter);
                if (res > 0)
                {
                    ex += "Cập nhật thành công dữ liệu trong ToolJig_Manager" + Environment.NewLine;
                }
                else
                {
                    ex += "Không ghi được dữ liệu vào ToolJig Manager. Tool/Jig có thể đã được khai báo trước đó hãy kiểm tra lại." + Environment.NewLine;
                }
                //Xử lý dữ liệu trong Qualification
                //var areaID = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, $"SELECT [AreaID] FROM[PIMV].[dbo].[MMCV_Areas] Where AreaName = '{item.Area}'");
                //var modelID = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, $"SELECT [SerialID] FROM [PIMV].[dbo].[MMCV_Models] Where Model = '{item.Model}'");
                //if (areaID != null && modelID != null)
                //{
                //    object[] parameter_qualification = new object[] { (int)areaID, (int)modelID, item.Line, item.ProcessID, item.Jig_Name, item.DeviceID, item.ReportLink, item.StaffNo };
                //    string query_qualification = "Insert Into MQ_ToolJigQualification ([AreaID],[ProductModelID],[LineID],[ProcessID],[ToolJigID],[MachineID],[ReportLink],[ApprovedDate],[Status],[DateUpdate],[UserUpdate]) " +
                //    "Values ( @AreaID , @ProductModelID , @LineID , @ProcessID , @ToolJigID , @MachineID , @ReportLink , GetDate() , 1 , GetDate() , @UserUpdate );";
                //    var res_qualification = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query_qualification, parameter_qualification);
                //    if (res_qualification > 0)
                //    {
                //        ex += "Cập nhật thành công dữ liệu trong Qualification" + Environment.NewLine;
                //    }
                //    else
                //    {
                //        ex += "Không thể ghi vào Qualification." + Environment.NewLine;
                //    }
                //}
                return true;
            }
            catch (Exception err)
            {
                ex += err.Message;
                return false;
            }



        }

        public bool AddMultiTools(out string exception, string STR_Tools, string area, string lineId, string model, string process, string tool_type, string maintenance_type,
            string partnumber, string punch_limit, string punch_warning, string use_days, string warning_days, bool OnlyOnce, string staffno, string sum)
        {
            exception = "";
            try
            {
                string STRconnect_times = "Server = ********** ; database = PIMV; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                string STRconnect_qualification = "Server = ********** ; database = PIMV; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                string STRconnect_mat;

                switch (area)
                {
                    case "SMT-AB":
                        STRconnect_mat = "Server = **********; database = Record; Uid = pim; Pwd = *******; MultipleActiveResultSets = True";
                        break;
                    case "SMT-C":
                        STRconnect_mat = "Server = **********; database = Record; Uid = pim; Pwd = *******; MultipleActiveResultSets = True";
                        break;
                    default:
                        STRconnect_mat = "";
                        break;
                }

                int punch_times = 0;
                string start_day = DateTime.Now.ToString("yyyy-MM-dd");
                int reset_times = 0;
                int punch_total = 0;
                int status = 1;
                int boad_types = ReferentTypeID(tool_type);

                if (!string.IsNullOrEmpty(STR_Tools))
                {
                    var builder_mat = new StringBuilder();
                    var builder_times = new StringBuilder();
                    var builder_qualification = new StringBuilder();
                    var tem = STR_Tools.Split(',');

                    //
                    var areaID = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, $"SELECT [AreaID] FROM[PIMV].[dbo].[MMCV_Areas] Where AreaName = '{area}'");
                    var modelID = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, $"SELECT [SerialID] FROM [PIMV].[dbo].[MMCV_Models] Where Model = '{model}'");
                    var sumDevice = int.Parse(sum);
                    string device_first = process + lineId;



                    foreach (var tool_item in tem)
                    {
                        //Check device
                        for (int i = 1; i <= sumDevice; i++)
                        {
                            string device_last = i.ToString("00");
                            string device = device_first + device_last;
                            string query_device = $"Insert Into [ToolJig_Devices] ([ToolJigID],[DeviceID]) Values ('{tool_item}', '{device}');";
                            builder_times.AppendLine(query_device);

                            string query_qualification = "Insert Into MQ_ToolJigQualification ([AreaID],[ProductModelID],[LineID],[ProcessID],[ToolJigID],[MachineID],[ApprovedDate],[Status],[DateUpdate],[UserUpdate]) " +
                            $"Values ({areaID}, '{modelID}', '{lineId}' , '{process}' , '{tool_item}' , '{device}', FORMAT(GETDATE(), 'yyyy-MM-dd HH:mm:ss') , 1 , FORMAT(GETDATE(), 'yyyy-MM-dd') , '{staffno}' );";

                            builder_qualification.AppendLine(query_qualification);
                        }

                        //Kiểm tra jig có cần insert sang BoadManager không
                        if (boad_types != 9999)
                        {
                            string query_mat = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line]) " +
                           $"Values ('{tool_item}','{boad_types}', GETDATE() ,{punch_limit},0,0,0,0,'','{lineId}');";
                            builder_mat.AppendLine(query_mat);
                        }

                        string query_times = "Insert into [ToolJig_TimesPunch] ([Area], [Line], [Model], [ProcessID], [Jig_Name], [Jig_Type], [Type], [PartNumber], [Jig_Punch], [Jig_Limit], [WarningPunch], [StartedDate], [NumDaysUse], [WarningDay], [NumResets], [TotalResets], [OnlyOnce], [Status], [StaffNo] ) " +
                            $"Values ('{area}', '{lineId}', '{model}', '{process}', '{tool_item}', '{tool_type}', '{maintenance_type}', '{partnumber}', '{punch_times}', {(string.IsNullOrEmpty(punch_limit) ? "NULL" : punch_limit)}, {(string.IsNullOrEmpty(punch_warning) ? "NULL" : punch_warning)}, '{start_day}', {(string.IsNullOrEmpty(use_days) ? "NULL" : use_days)}, {(string.IsNullOrEmpty(warning_days) ? "NULL" : warning_days)}, '{reset_times}', '{punch_total}', {(OnlyOnce ? 1 : 0)}, '{status}', '{staffno}' );" +
                            $"Update ToolJig_Warehouse set Status = 4 where Jig_Name = '{tool_item}';";
                        builder_times.AppendLine(query_times);
                    }

                    if (!string.IsNullOrEmpty(builder_times.ToString()))
                    {
                        SqlConnection conn_mat = null;
                        if (boad_types != 9999 && !string.IsNullOrEmpty(STRconnect_mat) && !string.IsNullOrEmpty(builder_mat.ToString()))
                            conn_mat = new SqlConnection(STRconnect_mat);
                        var conn_times = new SqlConnection(STRconnect_times);
                        var conn_qualification = new SqlConnection(STRconnect_qualification);
                        //Mở kết nối tới database
                        if (conn_mat != null) conn_mat.Open();
                        conn_times.Open();
                        conn_qualification.Open();

                        using (SqlTransaction tran_times = conn_times.BeginTransaction())
                        using (SqlTransaction tran_qualification = conn_qualification.BeginTransaction())
                        using (SqlTransaction tran_mat = conn_mat?.BeginTransaction())
                        {
                            try
                            {
                                using (SqlCommand cmd_times = new SqlCommand(builder_times.ToString(), conn_times, tran_times))
                                {
                                    cmd_times.ExecuteNonQuery();
                                }

                                using (SqlCommand cmd_qualification = new SqlCommand(builder_qualification.ToString(), conn_qualification, tran_qualification))
                                {
                                    cmd_qualification.ExecuteNonQuery();
                                }

                                if (tran_mat != null)
                                {
                                    using (SqlCommand cmd_mat = new SqlCommand(builder_mat.ToString(), conn_mat, tran_mat))
                                    {
                                        cmd_mat.ExecuteNonQuery();
                                    }
                                    tran_mat.Commit();
                                }
                                tran_times.Commit();
                                tran_qualification.Commit();
                                return true;
                            }
                            catch (Exception ex)
                            {
                                tran_times.Rollback();
                                tran_mat?.Rollback();
                                tran_qualification.Rollback();
                                exception = ex.Message;
                                return false;
                            }
                        }
                    }
                    else
                    {
                        exception = "No data to process.";
                        return false;
                    }
                }
                else
                {
                    exception = "Tools is null or empty";
                    return false;
                }
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return false;
            }
        }


        int ReferentTypeID(string tool_type)
        {
            switch (tool_type)
            {
                case "Stencil":
                    return 0;
                case "Jig plate":
                    return 1;
                case "Cover jig":
                    return 2;
                case "Support Block":
                    return 3;
                default:
                    return 9999;
            }
        }


        DataProvider.MMCV_Server Get_ServerSMT(string ServerName)
        {
            switch (ServerName)
            {
                case "SMT-AB":
                    return DataProvider.MMCV_Server.Server46_Record;
                case "SMT-C":
                    return DataProvider.MMCV_Server.Server69_Record;
                default:
                    return DataProvider.MMCV_Server.NULL;
            }
        }
    }
}
