﻿using System;
using System.Data;

namespace Models.OBA
{
    public class OBA_MasterRequest
    {
        private int serialID;
        private string model;
        private int index;
        private string itemName;
        private string pathEvidence;
        private string pathResult;
        private string staffNo;
        private DateTime? enterTime;
        public int SerialID { get => serialID; set => serialID = value; }
        public string Model { get => model; set => model = value; }
        public int Index { get => index; set => index = value; }
        public string ItemName { get => itemName; set => itemName = value; }
        public string PathEvidence { get => pathEvidence; set => pathEvidence = value; }
        public string PathResult { get => pathResult; set => pathResult = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }

        public OBA_MasterRequest() { }
        public OBA_MasterRequest(DataRow row)
        {
            this.SerialID = (int)row["SerialID"];
            this.Model = row["Model"].ToString();
            this.Index = (int)row["Index"];
            this.ItemName = row["ItemName"].ToString();
            this.PathEvidence = row["PathEvidence"].ToString();
            this.PathResult = row["PathResult"].ToString();
            this.StaffNo = row["StaffNo"].ToString();
            this.EnterTime = (DateTime?)row["EnterTime"];
        }
    }
}
