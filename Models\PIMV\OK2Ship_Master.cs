﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class OK2Ship_Master_EXC
    {
        public static DataTable Get_OK2Ship_Master(out string exception, string model = "")
        {
            string query = "Select * From [OK2Ship_Master]";
            if (!string.IsNullOrEmpty(model)) query += $" Where [Model] = '{model}'";
            return ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV,query);
        } 

        public static bool Add_OK2Ship_Master(out string exception, string model, string items, string updater)
        {
            var para = new object[] { model,items, updater };
            string query = "Insert Into [OK2Ship_Master] ([Model],[Items],[Updater],[EnterTime]) Values ( @Model , @Items , @Updater , GetDate() );";
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, para);
            return res > 0;
        }
        public static bool Update_OK2Ship_Master(out string exception, string model, string items, string updater, int id)
        {
            var para = new object[] { model, items, updater, id };
            string query = "Update [OK2Ship_Master] Set [Model] = @Model ,[Items] = @Items ,[Updater] = @Updater ,[EnterTime] = GetDate() Where SerialId = @SerialId ;";
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, para);
            return res > 0;
        }
    }
}
