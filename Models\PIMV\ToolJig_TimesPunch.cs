﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class ToolJig_TimesPunch
    {
        private int serialID;
        private string area;
        private string line;
        private string model;
        private string processID;
        //private string deviceID;
        private string type;
        private DateTime? startedDate;
        private int? numDaysUse;
        private int? numDaysUnUsed;
        private DateTime? lastTimeUsed;
        private DateTime? expirationDate;
        private int? warningDay;
        private string jig_Name;
        private string jig_Type;
        private string shtBarcode;
        private string partNumber;
        private int? jig_Punch;
        private int? jig_Limit;
        private int? warningPunch;
        private int? numResets;
        private long? totalResets;
        private bool? onlyOnce;
        private int? status;
        private string statusContent;
        private string staffNo;
        private DateTime? lastTime;
        private string reportLink;



        public int SerialID { get => serialID; set => serialID = value; }
        public string StatusContent { get => statusContent; set => statusContent = value; }
        public string Area { get => area; set => area = value; }
        public string Line { get => line; set => line = value; }
        public string Model { get => model; set => model = value; }
        public string ProcessID { get => processID; set => processID = value; }
        //public string DeviceID { get => deviceID; set => deviceID = value; }
        public string Type { get => type; set => type = value; }
        public string Jig_Name { get => jig_Name; set => jig_Name = value; }
        public string Jig_Type { get => jig_Type; set => jig_Type = value; }
        public string ShtBarcode { get => shtBarcode; set => shtBarcode = value; }
        public string PartNumber { get => partNumber; set => partNumber = value; }
        public int? Jig_Punch { get => jig_Punch; set => jig_Punch = value; }
        public int? WarningPunch { get => warningPunch; set => warningPunch = value; }
        public int? Jig_Limit { get => jig_Limit; set => jig_Limit = value; }
        public DateTime? StartedDate { get => startedDate; set => startedDate = value; }
        public int? NumDaysUse { get => numDaysUse; set => numDaysUse = value; }
        public int? NumDaysUnUsed { get => numDaysUnUsed; set => numDaysUnUsed = value; }
        public int? WarningDay { get => warningDay; set => warningDay = value; }
        public DateTime? LastTimeUsed { get => lastTimeUsed; set => lastTimeUsed = value; }
        public DateTime? ExpirationDate { get => expirationDate; set => expirationDate = value; }
        public int? NumResets { get => numResets; set => numResets = value; }
        public long? TotalResets { get => totalResets; set => totalResets = value; }
        public bool? OnlyOnce { get => onlyOnce; set => onlyOnce = value; }
        public int? Status { get => status; set => status = value; }

        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? LastTime { get => lastTime; set => lastTime = value; }
        public string ReportLink { get => reportLink; set => reportLink = value; }

        public ToolJig_TimesPunch() { }
        public ToolJig_TimesPunch(DataRow row)
        {
            //Serial ID
            this.SerialID = (int)row["SerialID"];
            this.Area = row["Area"].ToString();
            this.Line = row["Line"].ToString();
            this.Model = row["Model"].ToString();
            //ProcessID
            this.ProcessID = row["ProcessID"].ToString();
            //DeviceID
            //this.DeviceID = row["DeviceID"].ToString();
            //Type
            this.Type = row["Type"].ToString();
            //Jig_Name
            this.Jig_Name = row["Jig_Name"].ToString();
            this.Jig_Type = row["Jig_Type"].ToString();
            //ShtBarcode
            this.ShtBarcode = row["ShtBarcode"].ToString();
            //Part Number
            this.PartNumber = row["PartNumber"].ToString();
            //JIG_punch
            if (row["Jig_Punch"].ToString() == "")
            {
                this.Jig_Punch = null;
            }
            else
            {
                this.Jig_Punch = (int)row["Jig_Punch"];
            }
            //WarningPunch
            if (string.IsNullOrEmpty(row["WarningPunch"].ToString()))
            {
                this.WarningPunch = null;
            }
            else { this.WarningPunch = (int)row["WarningPunch"]; }
            //Jig_Limit
            if (string.IsNullOrEmpty(row["Jig_Limit"].ToString()))
            {
                this.Jig_Limit = null;
            }
            else this.Jig_Limit = (int)row["Jig_Limit"];
            //Number reset
            this.NumResets = (int)row["NumResets"];
            //Number Total reset
            if (string.IsNullOrEmpty(row["TotalResets"].ToString()))
            {
                this.TotalResets = null;
            }
            else { this.TotalResets = (long)row["TotalResets"]; }
            //Start date
            if (string.IsNullOrEmpty(row["StartedDate"].ToString())) this.StartedDate = null;
            else this.StartedDate = (DateTime)row["StartedDate"];
            //Num day use
            if (!string.IsNullOrEmpty(row["NumDaysUse"].ToString())) { this.NumDaysUse = (int)row["NumDaysUse"]; }
            if (!string.IsNullOrEmpty(row["NumDaysUnUsed"].ToString())) { this.NumDaysUnUsed = (int)row["NumDaysUnUsed"]; }
            //Last Time
            if (row["LastTimeUsed"].ToString() == "")
            {
                this.LastTimeUsed = null;
            }
            else
            {
                this.LastTimeUsed = (DateTime)row["LastTimeUsed"];
            }
            //WarningDay
            if (string.IsNullOrEmpty(row["WarningDay"].ToString()))
            {
                this.WarningDay = null;
            }
            else { this.WarningDay = (int)row["WarningDay"]; }
            //ExpirationDate
            if (this.StartedDate != null && this.NumDaysUse != null)
            {
                this.ExpirationDate = ((DateTime)StartedDate).AddDays((int)NumDaysUse);
            }
            //OnlyOnce
            if (string.IsNullOrEmpty(row["OnlyOnce"].ToString())) this.OnlyOnce = null;
            else this.OnlyOnce = (bool)row["OnlyOnce"];
            //Status
            this.Status = (int)row["Status"];
            switch (this.Status)
            {
                case 0:
                    this.StatusContent = "Need repair";
                    break;
                case 1:
                    this.StatusContent = "OK";
                    break;
                case 2:
                    this.StatusContent = "Warning";
                    break;
                case 3:
                    this.StatusContent = "Requested";
                    break;
                default:
                    break;
            }
            //StaffNo
            this.StaffNo = row["StaffNo"].ToString();
            //Last Time
            if (row["LastTime"].ToString() == "")
            {
                this.LastTime = null;
            }
            else
            {
                this.LastTime = (DateTime)row["LastTime"];
            }
            //Report link
            this.ReportLink = row["ReportLink"].ToString();
        }

        public bool CheckValue()
        {
            if (string.IsNullOrEmpty(Area) || string.IsNullOrEmpty(Line) ||
                string.IsNullOrEmpty(Model) || string.IsNullOrEmpty(ProcessID) || //string.IsNullOrEmpty(DeviceID) ||
                string.IsNullOrEmpty(Jig_Name) || string.IsNullOrEmpty(PartNumber) || OnlyOnce == null)
                return false;
            else
            {
                switch (Type)
                {
                    case "Times Punch":
                        if (Jig_Punch == null || Jig_Limit == null || WarningPunch == null) return false;
                        else return true;
                    case "Used Time":
                        if (StartedDate == null || NumDaysUse == null || WarningDay == null) return false;
                        else return true;
                    default:
                        return false;
                }
            }

        }
    }
    public class ToolJig_TimesPunch_EXC
    {
        public static bool? ExistJIG(out string exception, string JigID)
        {
            string query = $"Select Count(*) from [ToolJig_TimesPunch] Where Jig_Name = '{JigID}';";
            var res = SERVER_PV.Instance.ExcuteScalar(out exception, SERVER_DF.Instance.SV48_PIMV, query);
            if (string.IsNullOrEmpty(exception))
            {
                var nonQuery = int.Parse(res.ToString());
                return nonQuery > 0;
            }
            else
            {
                return null;
            }
        }
        public static bool UpdateLineProcesDevice(out string exception, string JigID, string LineID)
        {
            //string query_timespunch = $"Update [ToolJig_TimesPunch] set Line = '{LineID}', ProcessID = '{ProcessID}' Where Jig_Name = '{JigID}';";
            string query_timespunch = $"Update [ToolJig_TimesPunch] set Line = '{LineID}' Where Jig_Name = '{JigID}';";
            //string query_qualification = $"Update [MQ_ToolJigQualification] Set LineID = '{LineID}', ProcessID = '{ProcessID}' Where ToolJigID = '{JigID}';";
            string query_qualification = $"Update [MQ_ToolJigQualification] Set LineID = '{LineID}' Where ToolJigID = '{JigID}';";
            //string new_device = ProcessID + LineID;
            //string update_devices = $"Update [ToolJig_Devices] Set DeviceID = CONCAT('{new_device}',RIGHT(DeviceID,2)) Where ToolJigID = '{JigID}';";
            string update_devices = $"Update [ToolJig_Devices] Set DeviceID = CONCAT(LEFT(DeviceID, LEN(DeviceID) -6), '{LineID}', RIGHT(DeviceID, 2)) Where ToolJigID = '{JigID}';";

            //string update_devicesQuanlification = $"Update [MQ_ToolJigQualification] Set MachineID = CONCAT('{new_device}',RIGHT(MachineID,2)) Where ToolJigID = '{JigID}';";
            string update_devicesQuanlification = $"Update [MQ_ToolJigQualification] Set MachineID = CONCAT(LEFT([MachineID], LEN([MachineID]) - 6), '{LineID}', RIGHT([MachineID], 2)) Where ToolJigID = '{JigID}';";

            string query_boardmanage = $"Update BoardManage Set Line = '{LineID}' Where Barcode = '{JigID}';";
            SERVER_PV.Instance.ExcuteNonQuery(out _, SERVER_DF.Instance.SV46_Record, query_boardmanage);
            SERVER_PV.Instance.ExcuteNonQuery(out _, SERVER_DF.Instance.SV69_Record, query_boardmanage);

            SERVER_PV.Instance.ExcuteNonQuery(out exception, SERVER_DF.Instance.SV48_PIMV, query_timespunch);
            if (!string.IsNullOrEmpty(exception)) return false;
            SERVER_PV.Instance.ExcuteNonQuery(out exception, SERVER_DF.Instance.SV48_PIMV, update_devices);
            if (!string.IsNullOrEmpty(exception)) return false;
            SERVER_PV.Instance.ExcuteNonQuery(out exception, SERVER_DF.Instance.SV68_PIMV, query_qualification);
            if (!string.IsNullOrEmpty(exception)) return false;
            SERVER_PV.Instance.ExcuteNonQuery(out exception, SERVER_DF.Instance.SV68_PIMV, update_devicesQuanlification);
            if (!string.IsNullOrEmpty(exception)) return false;
            return true;
        }

        public static List<string> Get_ShortenedJIG(out string exception)
        {
            string query = "Select Distinct(LEFT(Jig_Name,10)) Shortened From ToolJig_TimesPunch;";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC48_PIMV, query);
            if (data != null && data.Rows.Count > 0)
            {
                return data.AsEnumerable().Select(shortened => shortened.Field<string>("Shortened")).ToList();
            }
            else
            {
                return null;
            }
        }
        public static bool ToolJig_Delete(string JigName)
        {
            string query = $"Delete From [ToolJig_TimesPunch] Where [Jig_Name] = '{JigName}'; " +
                           $"Delete FROM [PIMV].[dbo].[ToolJig_Devices] Where ToolJigID = '{JigName}';";
            var res = DataProvider.Instance.ExcuteNonTransaction(DataProvider.MMCV_Server.Server48_PIMV, query);
            if (res > 0)
            {
                string query_quantification = $"Delete From [MQ_ToolJigQualification] Where ToolJigID = '{JigName}';";
                DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query_quantification);
                string query_ManagerBoad = $"Delete FROM [Record].[dbo].[BoardManage] Where Barcode = '{JigName}'";
                DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server46_Record, query_ManagerBoad);
                DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server69_Record, query_ManagerBoad);
                return true;
            }
            else
            {
                return false;
            }
        }
        public static List<ToolJig_TimesPunch> LIST_TimesPunch(out string exception, string area = null, string line = null, string model = null, string processId = null, string jigType = null, string jigId = null)
        {
            exception = string.Empty;
            string condition = string.Empty;            
            if (!string.IsNullOrEmpty(area))
            {
                if (string.IsNullOrEmpty(condition)) condition = $"Where Area = '{area}'";
                else condition += $"And Area = '{area}'";
            }

            if (!string.IsNullOrEmpty(line))
            {
                if (string.IsNullOrEmpty(condition)) condition = $"Where Line = '{line}'";
                else condition += $"And Line = '{line}'";
            }

            if (!string.IsNullOrEmpty(model))
            {
                if (string.IsNullOrEmpty(condition)) condition = $"Where Model = '{model}'";
                else condition += $"And Model = '{model}'";
            }

            if (!string.IsNullOrEmpty(processId))
            {
                if (string.IsNullOrEmpty(condition)) condition = $"Where [ProcessID] = '{processId}'";
                else condition += $"And ProcessID = '{processId}'";
            }

            if (!string.IsNullOrEmpty(jigType))
            {
                if (string.IsNullOrEmpty(condition)) condition = $"Where [Jig_Type] = '{jigType}'";
                else condition += $"And Jig_Type = '{jigType}'";
            }

            if (!string.IsNullOrEmpty(jigId))
            {
                if (string.IsNullOrEmpty(condition)) condition = $"Where [Jig_Name] like '%{jigId}%'";
                else condition += $"And [Jig_Name] like '%{jigId}%'";
            }

            if (string.IsNullOrEmpty(condition))
            {
                return null;
            }
            else
            {
                string query = $"Select * from [ToolJig_TimesPunch] {condition}";
                var data = SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV48_PIMV, query);
                if (data != null && data.Rows.Count > 0)
                {
                    var myList = new List<ToolJig_TimesPunch>();
                    foreach (DataRow row in data.Rows) myList.Add(new ToolJig_TimesPunch(row));
                    return myList;
                }
                else
                {
                    return null;
                }
            }            
        }
    }
}
