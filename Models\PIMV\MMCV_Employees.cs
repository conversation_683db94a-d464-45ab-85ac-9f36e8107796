﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class MMCV_Employees
    {
        private int? serialID;
        private string employeesCode;
        private string fullName;
        private DateTime? hireDate;
        private DateTime? maternityLeaveDate;
        private DateTime? resignationDate;
        private string department;
        private string section;
        private string position;
        private string costCenter;
        private int? status;

        public int? SerialID { get => serialID; set => serialID = value; }
        public string EmployeesCode { get => employeesCode; set => employeesCode = value; }
        public string FullName { get => fullName; set => fullName = value; }
        public DateTime? HireDate { get => hireDate; set => hireDate = value; }
        public DateTime? MaternityLeaveDate { get => maternityLeaveDate; set => maternityLeaveDate = value; }
        public DateTime? ResignationDate { get => resignationDate; set => resignationDate = value; }
        public string Department { get => department; set => department = value; }
        public string Section { get => section; set => section = value; }
        public string Position { get => position; set => position = value; }
        public string CostCenter { get => costCenter; set => costCenter = value; }
        public int? Status { get => status; set => status = value; }

        public bool CheckData(string type = "Add")
        {
            if (string.IsNullOrEmpty(this.employeesCode)) return false;
            if (string.IsNullOrEmpty(this.fullName)) return false;
            if (this.hireDate == null) return false;
            if (string.IsNullOrEmpty(this.department)) return false;
            if (string.IsNullOrEmpty(this.section)) return false;
            if (string.IsNullOrEmpty(this.position)) return false;
            if (string.IsNullOrEmpty(this.costCenter)) return false;
            if (this.status == null) return false;
            if (type == "Edit") if (this.employeesCode == null) return false;
            return true;
        }

        public MMCV_Employees() { }
        public MMCV_Employees(DataRow row)
        {
            this.serialID = (int?)row["SerialID"];
            this.employeesCode = row["EmployeesCode"].ToString();
            this.fullName = row["FullName"].ToString();

            if (row["HireDate"] == DBNull.Value) this.hireDate = null;
            else this.hireDate = (DateTime?)row["HireDate"];

            if (row["MaternityLeaveDate"] == DBNull.Value) this.maternityLeaveDate = null;
            else this.maternityLeaveDate = (DateTime?)row["MaternityLeaveDate"];

            if (row["ResignationDate"] == DBNull.Value) this.resignationDate = null;
            else this.resignationDate = (DateTime?)row["ResignationDate"];

            this.department = row["Department"].ToString();
            this.section = row["Section"].ToString();
            this.position = row["Position"].ToString();
            this.costCenter = row["CostCenter"].ToString();
            this.status = (int?)row["Status"];
        }
    }

    public class MMCV_Employees_EXC
    {
        public static bool? Exist_Employees(out string exception, string EmployeesCode)
        {
            try
            {
                string query = $"Select count(*) from [MMCV_Employees] Where [EmployeesCode] = '{EmployeesCode}'";
                var res = SERVER_PV.Instance.ExcuteScalar(out exception, SERVER_DF.Instance.SV68_PIMV, query);
                if (res != null)
                {
                    var len = int.Parse(res.ToString());
                    return len > 0;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return null;
            }

        }
        public static bool Add_Employees(out string exception, MMCV_Employees employees)
        {
            string query = "Insert Into [MMCV_Employees] ([EmployeesCode],[FullName],[HireDate],[MaternityLeaveDate],[ResignationDate],[Department],[Section],[Position],[CostCenter],[Status]) " +
                "Values ( @EmployeesCode , @FullName , @HireDate , @MaternityLeaveDate , @ResignationDate , @Department , @Section , @Position , @CostCenter , @Status )";
            var parameter = new object[] { employees.EmployeesCode, employees.FullName, employees.HireDate,
                employees.MaternityLeaveDate,employees.ResignationDate,employees.Department,
                employees.Section,employees.Position,employees.CostCenter,employees.Status};
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, parameter);
            return res > 0;
        }
        //Edit
        public static bool Edit_Employees(out string exception, MMCV_Employees employees)
        {
            string query = "UPDATE [MMCV_Employees] " +
                           $"SET [FullName] = N'{employees.FullName}', [HireDate] = '{employees.HireDate}',[MaternityLeaveDate] = '{employees.MaternityLeaveDate}',[ResignationDate] = '{employees.ResignationDate}',[Department] = '{employees.Department}',[Section] = '{employees.Section}',[Position] = '{employees.Position}',[CostCenter] = '{employees.CostCenter}', [Status] = {employees.Status} " +
                           $"WHERE [EmployeesCode] = '{employees.EmployeesCode}';";
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return res > 0;
        }

        public static int Add_Employees(out string exception, List<MMCV_Employees> listEmployees)
        {
            var buiderQuery = new StringBuilder();
            foreach (var item in listEmployees)
            {
                string query = "Insert Into [MMCV_Employees] ([EmployeesCode],[FullName],[HireDate],[Department],[Section],[Position],[CostCenter],[Status]) " +
                $"Values ( '{item.EmployeesCode}' , N'{item.FullName}' , '{item.HireDate?.ToString("yyyy-MM-dd hh:mm:ss")}' , '{item.Department}' , '{item.Section}' , '{item.Position}' , '{item.CostCenter}' , {item.Status} );";
                buiderQuery.AppendLine(query);
            }
            if (string.IsNullOrEmpty(buiderQuery.ToString()))
            {
                exception = "Danh sách nhân viên trống.";
                return -1;
            }
            else
            {
                var res = SERVER_PV.Instance.ExcuteNonTrans(out exception, SERVER_DF.Instance.SV68_PIMV, buiderQuery.ToString());
                return res;
            }
        }
        public static DataTable Get_Employees(out string exception, MMCV_Employees employees = null)
        {
            string query = "Select * From MMCV_Employees";
            if (employees != null)
            {
                List<string> condition = new List<string>();
                foreach (var property in employees.GetType().GetProperties())
                {
                    var value = property.GetValue(employees);
                    if (value != null && !string.IsNullOrEmpty(value.ToString()))
                    {
                        if (property.Name == "Status") condition.Add($"Status = '{value.ToString().Substring(0, 1)}'");
                        else if (value is DateTime date) condition.Add($"{property.Name} = '{date.ToString("yyyy-MM-dd")}'");
                        else condition.Add($"{property.Name} = N'{value.ToString()}'");
                    }
                }

                if (condition.Count > 0)
                {
                    query += " Where " + string.Join(" And ", condition);
                }
            }

            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return data;
        }
    }
}
