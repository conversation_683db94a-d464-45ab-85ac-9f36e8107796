﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    class LAB_MasterRequest
    {
    }

    public class LAB_MasterRequest_EXC
    {
        public static DataTable MasterRequest_DataTable(out string exception,string Type)
        {
            string query = "Select * From LAB_MasterRequest With(Nolock) Where [Type] = '" + Type + "' Order by Model , [Index]";
            DataTable data =SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV68_PIMV, query);
            return data;
        }

        public static DataTable Get_DataMaterRequest(out string exception, string Type, string _model, string _Item, string _Index = null, string _KeyProcess = null, string _Frequency = null, string _Quantity = null, string _Criteria = null, string _Location = null)
        {
            string query = "SELECT * FROM LAB_MasterRequest With(Nolock) Where [Type] = '" + Type + "'";

            if (!string.IsNullOrEmpty(_model))
            {
                query = query + " And [Model] = '" + _model + "'";
            }

            if (!string.IsNullOrEmpty(_Item))
            {
                query = query + " And [Item] = '" + _Item + "'";
            }
 
            if (!string.IsNullOrEmpty(_Index))
            {
                query = query + " And [Index] = " + _Index + "";
            }

            if (!string.IsNullOrEmpty(_KeyProcess))
            {
                query = query + " And [KeyProcess] = '" + _KeyProcess + "'";
            }
            if (!string.IsNullOrEmpty(_Frequency))
            {
                query = query + " And [Frequency] = '" + _KeyProcess + "'";
            }
            if (!string.IsNullOrEmpty(_Criteria))
            {
                query = query + " And [Criteria] LIKE '%" + _KeyProcess + "%'";
            }
            if (!string.IsNullOrEmpty(_Quantity))
            {
                query = query + " And [Quantity] = " + _Quantity + "";
            }

            DataTable data = SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV68_PIMV, query);
            return data;
        }

    }
}
