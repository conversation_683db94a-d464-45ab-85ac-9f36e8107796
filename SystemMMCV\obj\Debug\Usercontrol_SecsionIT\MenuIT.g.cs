﻿#pragma checksum "..\..\..\Usercontrol_SecsionIT\MenuIT.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "CC349994DF169F9EE01CDE679ADF27D4640ED9C70C7F36B1162CA20681485409"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.Usercontrol_SecsionIT;


namespace SystemMMCV.Usercontrol_SecsionIT {
    
    
    /// <summary>
    /// MenuIT
    /// </summary>
    public partial class MenuIT : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 17 "..\..\..\Usercontrol_SecsionIT\MenuIT.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton rbt_trouble;
        
        #line default
        #line hidden
        
        
        #line 18 "..\..\..\Usercontrol_SecsionIT\MenuIT.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton rbt_job;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\Usercontrol_SecsionIT\MenuIT.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card card_content;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_secsionit/menuit.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Usercontrol_SecsionIT\MenuIT.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.rbt_trouble = ((System.Windows.Controls.RadioButton)(target));
            
            #line 17 "..\..\..\Usercontrol_SecsionIT\MenuIT.xaml"
            this.rbt_trouble.Checked += new System.Windows.RoutedEventHandler(this.Event_CheckedAction);
            
            #line default
            #line hidden
            return;
            case 2:
            this.rbt_job = ((System.Windows.Controls.RadioButton)(target));
            
            #line 18 "..\..\..\Usercontrol_SecsionIT\MenuIT.xaml"
            this.rbt_job.Checked += new System.Windows.RoutedEventHandler(this.Event_CheckedAction);
            
            #line default
            #line hidden
            return;
            case 3:
            this.card_content = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

