﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.ORT
{
    public class ORT_Update
    {
        private long noID;
        private string model;
        private string mPN;
        private string itemName;
        private long serialID;
        private string lotno;
        private string nameStage;
        private string typeDeadline;
        private DateTime deadline;
        private string statusTest;

        public long NoID { get => noID; set => noID = value; }
        public string Model { get => model; set => model = value; }
        public string MPN { get => mPN; set => mPN = value; }
        public string ItemName { get => itemName; set => itemName = value; }
        public string Lotno { get => lotno; set => lotno = value; }
        public string NameStage { get => nameStage; set => nameStage = value; }
        public string TypeDeadline { get => typeDeadline; set => typeDeadline = value; }
        public DateTime Deadline { get => deadline; set => deadline = value; }
        public string StatusTest { get => statusTest; set => statusTest = value; }
        public long SerialID { get => serialID; set => serialID = value; }

        public ORT_Update() { }
        public ORT_Update(DataRow row)
        {
            this.NoID = (long)row["NoID"];
            this.Model = row["Model"].ToString();
            this.MPN = row["MPN"].ToString();
            this.ItemName = row["ItemName"].ToString();
            this.SerialID = (long)row["SerialID"];
            this.Lotno = row["Lotno"].ToString();
            this.NameStage = row["NameStage"].ToString();
            this.TypeDeadline = row["TypeDeadline"].ToString();
            this.Deadline = (DateTime)row["Deadline"];
            this.StatusTest = row["StatusTest"].ToString();
        }

    }
}
