﻿#pragma checksum "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "C06C982D7B224384A96054C1181C85A0750C82FCABC030976E384826D5DD79D8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_OK2SHIP;


namespace SystemMMCV.UserControl_OK2SHIP {
    
    
    /// <summary>
    /// OK2SHIP_MasterSample
    /// </summary>
    public partial class OK2SHIP_MasterSample : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 22 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShowInfo;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid grid_header;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_department;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_model;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_index;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_keyProcess;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_item;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_frequency;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_quantity;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_criteria;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_location;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnImport;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtFilePath;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid datagrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_ok2ship/ok2ship_mastersample.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ShowInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.grid_header = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.txt_department = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txt_model = ((System.Windows.Controls.ComboBox)(target));
            
            #line 39 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            this.txt_model.DropDownClosed += new System.EventHandler(this.Event_LoadComboboxItem);
            
            #line default
            #line hidden
            return;
            case 5:
            this.tbx_index = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.tbx_keyProcess = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.txt_item = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.tbx_frequency = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.tbx_quantity = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.tbx_criteria = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.tbx_location = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            
            #line 56 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Search);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 57 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Add);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 58 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Edit);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 59 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Refresh);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 60 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Export);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btnImport = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            this.btnImport.Click += new System.Windows.RoutedEventHandler(this.Event_Import);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 62 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Browse);
            
            #line default
            #line hidden
            return;
            case 19:
            this.txtFilePath = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.datagrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 69 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            this.datagrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.Event_ChangeSelectDataGrip);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 21:
            
            #line 88 "..\..\..\UserControl_OK2SHIP\OK2SHIP_MasterSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Delete);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

