﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    class ToolJig_Devices
    {
    } 
    public class ToolJig_Devices_EXC
    {
        public static DataTable GET_Devices(out string exception,string jigId)
        {
            string query = $"Select * from ToolJig_Devices With(NoLock) Where [ToolJigID] = '{jigId}'";
            return SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV48_PIMV, query);
        }
        

    }
}
