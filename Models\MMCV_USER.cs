﻿
using System.Data;

namespace Models
{
    public class MMCV_USER
    {
        private string username;
        private string password;
        private bool isAdmin;
        private string fullName;
        private string department;
        private string groupName;

        public string Username { get => username; set => username = value; }
        public string Password { get => password; set => password = value; }
        public bool IsAdmin { get => isAdmin; set => isAdmin = value; }
        public string FullName { get => fullName; set => fullName = value; }
        public string Department { get => department; set => department = value; }        
        public string GroupName { get => groupName; set => groupName = value; }

        public MMCV_USER() { }
        public MMCV_USER(DataRow row) 
        {
            this.Username = row["Username"].ToString();
            this.Password = row["Password"].ToString();
            this.IsAdmin = (bool)row["IsAdmin"];
            this.FullName = row["FullName"].ToString();
            this.Department = row["Department"].ToString();
            this.GroupName = row["GroupName"].ToString();
        }

        public void SetValue(DataRow row)
        {
            this.Username = row["Username"].ToString();
            this.Password = row["Password"].ToString();
            this.IsAdmin = (bool)row["IsAdmin"];
            this.FullName = row["FullName"].ToString();
            this.Department = row["Department"].ToString();
            this.GroupName = row["GroupName"].ToString();
        }

        public void ResetValue()
        {
            this.Username = null;
            this.Password = null;
            this.IsAdmin = false;
            this.FullName = null;
            this.Department = null;
        }
    }
}
