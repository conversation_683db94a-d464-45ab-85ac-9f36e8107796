﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class OK2Ship_Results
    {
        public string Model { get; set; }
        public string ProgramName { get; set; }
        public string Build { get; set; }
        public string Config { get; set; }
        public string PickDate { get; set; }
        public string Shift { get; set; }
        public string Line { get; set; }
        public string Lotno { get; set; }
        public int Quantity { get; set; }
        public string MPN { get; set; }
        public string Result { get; set; }
        
    }

    public class OK2Ship_Results_EXC
    {

        public static bool Send_Request(out string exception, OK2Ship_Results info)
        {
            var username = Models.UserProvider.Instance.DataUser.Username;
            string query = "Insert Into [OK2Ship_Results] ([ProgramName],[Model],[Build],[Config],[Creator],[TimeCreate],[Lotno],[Result],[Quantity]) " +
                $"values ('{info.ProgramName}','{info.Model}','{info.Build}','{info.Config}','{username}',GetDate(),'{info.Lotno}','{info.Result}','{info.Quantity}');";
            var res = SERVER_PV.Instance.ExcuteNonQuery(out exception, SERVER_DF.Instance.SV68_PIMV, query);            
            return res > 0;
        }

        public static DataTable ListGetSample(out string exception)
        {
            string query = "SELECT [OK2ShipId],[ProgramName],[Model],[Build],[Config],[Creator],[TimeCreate],[Lotno],[Quantity],[Result] FROM [OK2Ship_Results] With(NoLock) Where Result = 'Request'";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return data;
        }
        public static DataTable ListNeedGetSample(out string exception)
        {
            string query = "Select * from OK2Ship_Results where Result = 'Request';";
            //string query = "Select A.ProgramName,A.Model,A.Build,A.Config,A.Lotno,A.TimeCreate, " +
            //    "B.[Index],B.Items,B.Quantity,B.Criteria,B.Notes,B.Performed,B.ItemStatus "+
            //    "from OK2Ship_Results A With(NoLock) " +
            //    "Inner Join LAB_Details B with(NoLock) On A.RequestNo = B.RequestNo "+
            //    "WHere A.Result = 'ON-GOING' And B.ItemStatus = 'Request'";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return data;
        }
        public static List<string> Get_Builds(out string exception)
        {
            //Select Distinct(Config) FROM[PIMV].[dbo].[OK2Ship_Results]  order by Config;
            string query = "Select Distinct(Build) FROM[PIMV].[dbo].[OK2Ship_Results] order by Build;";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            if (string.IsNullOrEmpty(exception))
            {
                if (data != null && data.Rows.Count > 0)
                {
                    return data.AsEnumerable()
                        .Select(row => row.Field<string>("Build"))
                        .ToList();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        public static List<string> Get_Config(out string exception)
        {
            string query = "Select Distinct(Config) FROM[PIMV].[dbo].[OK2Ship_Results]  order by Config;";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            if (string.IsNullOrEmpty(exception))
            {
                if (data != null && data.Rows.Count > 0)
                {
                    return data.AsEnumerable()
                        .Select(row => row.Field<string>("Config"))
                        .ToList();
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        public static bool ConfirmOK2SHIP(out string exception, int ok2ship_id, string result, string username, string filename)
        {
            var buider = new StringBuilder();
            buider.Append($"Update OK2Ship_Results Set Result = '{result}',Checker = '{username}', TimeCheck = GetDate() [FileName] = '{filename}' Where OK2ShipId = {ok2ship_id};");
            buider.Append($"Update OK2Ship_Details Set [Status] = 'LOCK' Where OK2ShipId = {ok2ship_id};");
            var res = ModelsProvider.SqlInstance.ExcuteNonTransaction(out exception, SqlProvider.SqlSV.OFC68_PIMV, buider.ToString());
            return res > 0;
        }


        public static DataTable Get_OK2Ship_Results(out string exception,string lotno,string model, string build, string config)
        {
            
            string condition = "";

            if (!string.IsNullOrEmpty(lotno))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" LotNo = '{lotno}'";
            }

            if (!string.IsNullOrEmpty(model))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" Model = '{model}'";
            }

            if (!string.IsNullOrEmpty(build))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" Build = '{build}'";
            }

            if (!string.IsNullOrEmpty(config))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" Config = '{config}'";
            }

            if (!string.IsNullOrEmpty(condition))
            {
                string query = "Select * From [OK2Ship_Results]" + condition;
                return ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            }
            else
            {
                return ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, "Select * From [OK2Ship_Results] Where [Result] ='ON-GOING' ");
            }
        }

        public static bool Add_OK2Ship_Results(out string exception, string model, string build, string config, string lotno, string program, string creator)
        {
            string query = "EXEC USP_OK2Ship_Resulst_Request @Model , @Build , @Config , @Lotno , @ProgramName , @Creator ;";
            var paramter = new object[] { model,build,config,lotno,program,creator};
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query,paramter);
            if (string.IsNullOrEmpty(exception))
            {                
                return res > 0;
            }
            else
            {
                return false;
            }
        }

        public static bool Update_OK2Ship_Results(out string exception, string model, string build, string config, string lotno, string program, string creator, int ok2shipId)
        {
            string query = "Update OK2Ship_Results Set [Model] = @Model ,[Build] = @Build ,[Config] = @Config , [Lotno] = @Lotno ,[ProgramName] = @ProgramName ,[Creator] = @Creator  Where OK2ShipId = @OK2ShipId ;";
            var parameter = new object[] { model, build, config, lotno, program, creator, ok2shipId };
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, parameter);
            if (string.IsNullOrEmpty(exception))
            {
                return res > 0;
            }
            else
            {
                return false;
            }
        }

        public static bool Update_OK2Ship_Confirm(out string exception,string filename,string result, int ok2shipiId)
        {
            var parameter = new object[] { filename, result, ok2shipiId };
            string query = "Update [OK2Ship_Results] set [FileName] = @FileName , [Result] = @Result Where [OK2ShipId] = @OK2ShipId ";
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, parameter);
            if (string.IsNullOrEmpty(exception))
            {
                return res > 0;
            }
            else
            {
                return false;
            }
        }
    }
}
