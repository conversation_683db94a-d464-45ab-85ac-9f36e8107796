﻿using System;
using System.Data;

namespace Models
{
    public class MMCV_Permission
    {
        private string username;
        private string functionID;
        private bool? isAccept;
        private string roles;
        private DateTime? enterTime;
        public string Username { get => username; set => username = value; }
        public string FunctionID { get => functionID; set => functionID = value; }
        public bool? IsAccept { get => isAccept; set => isAccept = value; }
        public string Roles { get => roles; set => roles = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }

        public MMCV_Permission() { }
        public MMCV_Permission(DataRow row)
        {
            this.Username = row["Username"].ToString();
            this.FunctionID = row["FunctionID"].ToString();
            this.IsAccept = (bool)row["IsAccept"];
            this.Roles = row["Roles"].ToString();
            if (!string.IsNullOrEmpty(row["EnterTime"].ToString()))
            {
                this.enterTime = (DateTime)row["EnterTime"];
            }
            else
            {
                this.enterTime = null;
            }
            
        }
    }

    
}
