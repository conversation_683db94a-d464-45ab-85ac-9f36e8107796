﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    class OK2Ship_Details
    {
    }
    public class OK2Ship_Details_EXC
    {
        public static DataTable Get_ItemByOK2ShipId(out string exception,int ok2ShipId)
        {
            string query = "Select Item SheetName,RequestNo,Items Folder from OK2Ship_Details A " +
                    "left Join LAB_Details B On A.LabDetails_ID = B.SerialID " +
                    $"Where OK2ShipId = {ok2ShipId} order by A.SerialId;";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return data;
        }

        public static bool MapLinkToOk2Ship(out string exception, string staffno, int ok2shipDetails_Id, string result, long labDetails_Id)
        {
            string query = "Update [OK2Ship_Details] set LabDetails_ID = @LabDetailsID , Updater = @Updater , EnterTime = GetDate() , Result = @Result Where SerialId = @SerialId ";
            var parameter = new object[] { labDetails_Id, staffno , result, ok2shipDetails_Id };
            var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, parameter);
            return res > 0;
        }
        public static DataTable Get_OK2Ship_DetailsMapResult(out string exception,string lotno,string model, string build, string config)
        {
            string query = "Select [SerialId],[ProgramName],[Model],[Build],[Config],[Lotno],Item,[EnterTime],[Updater],A.Result,[Status],[LabDetails_ID] " +
                    "From [OK2Ship_Details] A with(Nolock) Inner Join OK2Ship_Results B with(NoLock) ON A.OK2ShipId = B.OK2ShipId";
            string condition = "";

            if (!string.IsNullOrEmpty(lotno))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" LotNo = '{lotno}'";
            }


            if (!string.IsNullOrEmpty(model))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" Model = '{model}'";
            }

            if (!string.IsNullOrEmpty(build))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" Build = '{build}'";
            }

            if (!string.IsNullOrEmpty(config))
            {
                if (string.IsNullOrEmpty(condition)) condition = " Where";
                else condition += " And ";
                condition += $" Config = '{config}'";
            }

            if (!string.IsNullOrEmpty(condition))
            {
                return ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query + condition);
            }
            else
            {
                return ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query + " Where B.[Result] ='ON-GOING' ");
            }
        }
    }
}
