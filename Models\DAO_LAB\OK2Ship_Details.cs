﻿using System;
using System.Data;

namespace Models.DAO_LAB
{
    class OK2Ship_Details
    {
        private long serialID;
        private string invoice;
        private string department;
        private string requestNo;
        private string staffNo;
        private DateTime enterTime;
        public long SerialID { get => serialID; set => serialID = value; }
        public string Invoice { get => invoice; set => invoice = value; }
        public string Department { get => department; set => department = value; }
        public string RequestNo { get => requestNo; set => requestNo = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime EnterTime { get => enterTime; set => enterTime = value; }

        public OK2Ship_Details(DataRow row)
        {
            this.SerialID = (long)row["SerialID"];
            this.Invoice = row["Invoice"].ToString();
            this.Department = row["Department"].ToString();
            this.RequestNo = row["RequestNo"].ToString();
            this.StaffNo = row["StaffNo"].ToString();
            this.EnterTime = (DateTime)row["EnterTime"];
        }
    }
}
