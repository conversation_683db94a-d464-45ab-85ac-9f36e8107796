﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.Record
{
    public class BoardManage
    {
        public long BoardID { get; set; }
        public string Barcode { get; set; }
        public int BoardTypes { get; set; }
        public DateTime EnterTime { get; set; }
        public int MaintainFrequency { get; set; }
        public int AfterMaintainUseTimes { get; set; }
        public int ScrapLimitTimes { get; set; }
        public int HistoryUseTimes { get; set; }
        public int State { get; set; }
        public string Directory { get; set; }
        public string Line { get; set; }
        public string Area { get; set; }
        public BoardManage() { }
        public BoardManage(DataRow row, string Area)
        {
            this.BoardID = (long)row["BoardID"];
            this.Barcode = row["Barcode"].ToString();
            this.BoardTypes = (int)row["BoardTypes"];
            this.EnterTime = (DateTime)row["EnterTime"];
            this.MaintainFrequency = (int)row["MaintainFrequency"];
            this.AfterMaintainUseTimes = (int)row["AfterMaintainUseTimes"];
            this.ScrapLimitTimes = (int)row["ScrapLimitTimes"];
            this.HistoryUseTimes = (int)row["HistoryUseTimes"];
            this.State = (int)row["State"];
            this.Directory = row["Directory"].ToString();
            this.Line = row["Line"].ToString();
            this.Area = Area;
        }
    }
    public class BoardManage_EXC
    {
        public static List<BoardManage> SELECT_BoardManage(out string exception, string JigBarcode = "", string JigType = "")
        {
            string query = "Select * From [BoardManage]";
            string condition = "";
            if (!string.IsNullOrEmpty(JigBarcode))
            {
                if (string.IsNullOrEmpty(condition))
                    condition += $" Where [Barcode] = '{JigBarcode}'";
                else
                    condition += $" And [Barcode] = '{JigBarcode}'";
            }

            if (!string.IsNullOrEmpty(JigType))
            {
                if (string.IsNullOrEmpty(condition))
                    condition += $" Where [BoardTypes] = '{JigType}'";
                else
                    condition += $" And [BoardTypes] = '{JigType}'";
            }

            var data_46 = SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV46_Record, query + condition);
            if (!string.IsNullOrEmpty(exception)) return null;
            var data_69 = SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV69_Record, query + condition);
            if (!string.IsNullOrEmpty(exception)) return null;

            var list = new List<BoardManage>();
            try
            {
                foreach (DataRow row in data_46.Rows) list.Add(new BoardManage(row, "SMT-AB"));
                foreach (DataRow row in data_69.Rows) list.Add(new BoardManage(row, "SMT-C"));
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return null;
            }
            return list;
        }
        
        public static bool INSEART_BoardManage(out string exception, BoardManage item)
        {
            try
            {
                SERVER server;
                switch (item.Area)
                {
                    case "SMT-AB":
                        server = SERVER_DF.Instance.SV46_Record;
                        break;
                    case "SMT-C":
                        server = SERVER_DF.Instance.SV69_Record;
                        break;
                    default:
                        server = null;
                        break;
                }

                if (server != null)
                {
                    string query = $"Insert Into [BoardManage] ([Barcode],[BoardTypes],[Entertime],[MaintainFrequency],[AfterMaintainUseTimes],[ScrapLimitTimes],[HistoryUseTimes],[State],[Directory],[Line]) " +
                    "Values ( @Barcode , @BoardTypes , GetDate() , @MaintainFrequency , @AfterMaintainUseTimes , @ScrapLimitTimes , @HistoryUseTimes , @State , @Directory , @Line );";
                    var parameter = new object[] { item.Barcode, item.BoardTypes, item.MaintainFrequency, item.AfterMaintainUseTimes, item.ScrapLimitTimes, item.HistoryUseTimes, item.State, item.Directory, item.Line };
                    var res = SERVER_PV.Instance.ExcuteNonQuery(out exception, server, query, parameter);
                    return res > 0;
                }
                else
                {
                    exception = $"Không xác định Area: {item.Area}";
                    return false;
                }
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return false;
            }
        }

        public static bool RESET_BoardManage(out string exception, BoardManage item)
        {
            try
            {
                SERVER server;
                switch (item.Area)
                {
                    case "SMT-AB":
                        server = SERVER_DF.Instance.SV46_Record;
                        break;
                    case "SMT-C":
                        server = SERVER_DF.Instance.SV69_Record;
                        break;
                    default:
                        server = null;
                        break;
                }
                if (server != null)
                {
                    string query = $"Update [BoardManage] set HistoryUseTimes = 0 Where BoardID = {item.BoardID}";
                    var res = SERVER_PV.Instance.ExcuteNonQuery(out exception, server, query);
                    return res > 0;
                }
                else
                {
                    exception = $"Không xác định Area: {item.Area}";
                    return false;
                }
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return false;
            }
        }
    }
}
