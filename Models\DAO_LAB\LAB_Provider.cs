﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Text;

namespace Models.DAO_LAB
{
    public class LAB_Provider
    {
        public static void Reset()
        {
            instance = null;
        }

        private static LAB_Provider instance;
        public static LAB_Provider Instance { get { if (instance == null) instance = new LAB_Provider(); return instance; } private set => instance = value; }

        #region MesterItems
        public List<LAB_MesterItems> List_MasterItems()
        {
            string query = "Select * From [LAB_MasterItems]";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            if (data != null && data.Rows.Count > 0)
            {
                var myList = new List<LAB_MesterItems>();
                foreach (DataRow item in data.Rows)
                {
                    myList.Add(new LAB_MesterItems(item));
                }
                return myList;
            }
            else
            {
                return null;
            }
        }

        public static DataTable GET_Departmnet(out string exception)
        {
            try
            {
                string query = "SELECT Distinct(Department) FROM [PIMV].[dbo].[LAB_Results] with(nolock) Order by Department";
                return ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, null);
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return null;
            }
        }

        public bool MasterItems_Add(LAB_MesterItems master)
        {
            var parameter = new object[] { master.Model, master.ParentItem, master.Items, master.Performed, master.Listtime };
            string query = "Insert Into [LAB_MasterItems] ([Model],[ParentItem],[Items],[Performed],[Listtime]) Values ( @Model , @ParentItem , @Items , @Destination , @Listtime )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public List<LAB_MesterItems> MasterItems_Search(LAB_MesterItems master)
        {
            string strQuery = string.Format("Select * From [LAB_MasterItems] WHERE SerialID > 0");
            List<string> getParam = new List<string>();

            if (!string.IsNullOrEmpty(master.Model))
            {
                getParam.Add(string.Format(" Model = '{0}'", master.Model));
            }
            if (!string.IsNullOrEmpty(master.ParentItem))
            {
                getParam.Add(string.Format("  ParentItem = '{0}'", master.ParentItem));
            }
            if (!string.IsNullOrEmpty(master.Items))
            {
                getParam.Add(string.Format("  Items = '{0}'", master.Items));
            }
            if (!string.IsNullOrEmpty(master.Performed))
            {
                getParam.Add(string.Format("  Performed = '{0}'", master.Performed));
            }
            if (!string.IsNullOrEmpty(master.Listtime))
            {
                getParam.Add(string.Format("  Listtime = '{0}'", master.Listtime));
            }
            if (getParam.Count > 0)
            {
                if (getParam.Count == 1)
                {
                    strQuery += " and " + getParam[0];
                }
                if (getParam.Count > 1)
                {
                    strQuery += " and " + getParam[0];
                    for (int i = 1; i < getParam.Count; i++)
                    {
                        strQuery += " and " + getParam[i];
                    }
                }
            }
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, strQuery);
            if (data != null && data.Rows.Count > 0)
            {
                var myList = new List<LAB_MesterItems>();
                foreach (DataRow item in data.Rows)
                {
                    myList.Add(new LAB_MesterItems(item));
                }
                return myList;
            }
            else
            {
                return null;
            }
        }

        public bool MasterItems_Edit(LAB_MesterItems master)
        {
            string query = "Update [LAB_MasterItems] Set [Model] = @Model , [ParentItem] = @ParentItem , [Items] = @Item , [Performed] = @Performed , [Listtime] = @Listtime , [Entertime] = @EnterTime Where [SerialID] = @SerialID ";
            var parameter = new object[] { master.Model, master.ParentItem, master.Items, master.Performed, master.Listtime, master.Entertime, master.SerialID };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool MasterItems_Delete(int SerialID)
        {
            string query = "Delete From [LAB_MasterItems] Where [SerialID] = " + SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        public DataRow GetDataFromModelItem(string _model, string _item)
        {
            string query = "Select * From [LAB_MasterItems] Where [Model] = '" + _model + "' And [Items] = '" + _item + "'";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            if (data != null && data.Rows.Count > 0)
            {
                return data.Rows[0];
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region MasterRequest
        public bool MasterRequest_Add(LAB_MasterRequest master)
        {
            string query = "Insert Into [LAB_MasterRequest] ([Type],[Department],[Model],[Index],[KeyProcess],[Items],[Location],[Quantity],[Frequency],[Criteria],[StaffNo],[EnterTime]) " +
                    "Values ( @Type , @Department , @Model , @Index , @KeyProcess , @Items , @Location , @Quantity , @Frequency , @Criteria , @StaffNo , @EnterTime )";
            object[] para = new object[] { master.Type, master.Department, master.Model, master.Index, master.KeyProcess, master.Items, master.Location, master.Quantity, master.Frequency, master.Criteria, master.StaffNo, master.EnterTime };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, para);
            return res > 0;
        }

        public bool MasterRequest_AddTest(LAB_MasterRequest master)
        {
            string query = "Insert Into [LAB_MasterRequest_TEST] ([Type],[Department],[Model],[Index],[KeyProcess],[Items],[Location],[Quantity],[Frequency],[Criteria],[StaffNo],[EnterTime]) " +
                    "Values ( @Type , @Department , @Model , @Index , @KeyProcess , @Items , @Location , @Quantity , @Frequency , @Criteria , @StaffNo , @EnterTime )";
            object[] para = new object[] { master.Type, master.Department, master.Model, master.Index, master.KeyProcess, master.Items.ToString(), master.Location, master.Quantity, master.Frequency, master.Criteria, master.StaffNo, master.EnterTime };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, para);
            return res > 0;
        }

        public DataTable MasterRequest_DataTable(string Type, string Department)
        {
            string query = "Select * From LAB_MasterRequest With(Nolock) Where [Type] = '" + Type + "' And [Department] = '" + Department + "' Order by Model , [Index]";
            DataTable data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }

        public bool MasterRequest_Edit(LAB_MasterRequest master)
        {
            string query = "Update [LAB_MasterRequest] Set [KeyProcess] = N'" + master.KeyProcess +
                    "',[Items] = N'" + master.Items + "',[Location] = N'" + master.Location + "',[Quantity] = " + master.Quantity +
                    ",[Frequency]=N'" + master.Frequency + "',[Criteria] = N'" + master.Criteria +
                    "',[StaffNo] = '" + master.StaffNo + "',[EnterTime] = '" + master.EnterTime +
                    "' Where [Type] = '" + master.Type + "' And [Department] = '" + master.Department + "' And [Model] = '" + master.Model +
                    "' And [Index] = " + master.Index;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        public bool MasterRequest_Delete(int SerialID)
        {
            string query = "Delete From [LAB_MasterRequest] Where [SerialID] = " + SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        #endregion

        #region CreateRequest

        public string CreateRequestNo(string _department)
        {
            DateTime? _datetime = DataProvider.Instance.GetTimeSever(DataProvider.MMCV_Server.Server68_PIMV);
            if (_datetime == null)
            {
                return DateTime.Now.ToString("yyyyMMddhhmmss") + "_" + _department;
            }
            else
            {
                return _datetime.Value.ToString("yyyyMMddhhmmss") + "_" + _department;
            }
        }

        public bool IsWriteRequest(StringBuilder query)
        {
            var _res = DataProvider.Instance.ExcuteNonTransaction(DataProvider.MMCV_Server.Server68_PIMV, query.ToString());
            return _res > 0;
        }

        #endregion

        #region Confirm
        public DataTable Get_DataConfirm(string _performed, string _requestno, string _lotno = null, string _username = null, string _department = null)
        {
            string query = "SELECT [ProdDate],[Index],[Model],[MPN],[LotNo],[Line],[Shift],[Items],[Quantity],[Criteria],[Notes],[Performed],[ListTime],[ItemStatus],[TimeRequest],[UserRequest],[Department],[LAB_Details].[RequestNo] " +
                                "FROM[LAB_Details] Inner join[LAB_Results] On[LAB_Details].RequestNo = [LAB_Results].RequestNo " +
                                "Where [ItemStatus] = 'Request' And [Performed] = '" + _performed + "'";

            //Check RequestNo
            if (!string.IsNullOrEmpty(_requestno))
            {
                query = query + " And [LAB_Details].[RequestNo] = '" + _requestno + "'";
            }
            //Check Lotno
            if (!string.IsNullOrEmpty(_lotno))
            {
                query = query + " And [LotNo] = '" + _lotno + "'";
            }
            //Check User
            if (!string.IsNullOrEmpty(_username))
            {
                query = query + " And [UserRequest] = '" + _username + "'";
            }
            //Check Department
            if (!string.IsNullOrEmpty(_department))
            {
                query = query + " And [Department] = '" + _department + "'";
            }
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }



        public bool IsSet_Confirm(string status, string _requestno, int _index)
        {
            var paramester = new object[] { status, UserProvider.Instance.DataUser.Username, _requestno, _index };
            string query = "Update [LAB_Details] Set [ItemStatus] = @Status , [TimeConfirm] = GETDATE(), [UserConfirm] = @Username Where [RequestNo] = @RequestNo And [Index] = @Index ;";
            if (status == "Reject")
            {
                query = "Update [LAB_Details] Set [Result] = 'N/A' , [ItemStatus] = @Status , [TimeConfirm] = GETDATE(), [UserConfirm] = @Username Where [RequestNo] = @RequestNo And [Index] = @Index ;";
            }
            var _res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, paramester);
            if (status == "Reject") UpdateStatusResult(_requestno);
            return _res > 0;
        }


        #endregion

        #region Upload

        public long? Get_SerialDetail(string _RequestNo, int _Index)
        {
            string query = $"Select [SerialID] From [LAB_Details] Where RequestNo = '{_RequestNo}' And [Index] = {_Index}";
            var res = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, query);
            if (res != null)
            {
                return (long)res;
            }
            else
            {
                return null;
            }
        }
        public DataTable Get_DataUpload(string _performed, string _department, string _mpn, string _lotno = null, string _username = null)
        {
            string query = "SELECT [LAB_Details].SerialId,[Model],[MPN],[Index],[ProdDate],[LotNo],[Line],[Shift],[Items],[Quantity],[ErrorLimits],[ErrorQuantity],[Criteria],[Notes],[Performed],[ListTime],[ItemStatus],[TimeRequest],[UserRequest],[Department],[TimeConfirm],[UserConfirm],[LAB_Details].[RequestNo] " +
                                "FROM[LAB_Details] Inner join[LAB_Results] On[LAB_Details].RequestNo = [LAB_Results].RequestNo " +
                                "Where [ItemStatus] = 'Confirm' And [Performed] = '" + _performed + "'";
            //Check RequestNo
            if (!string.IsNullOrEmpty(_department))
            {
                query = query + " And [Department] = '" + _department + "'";
            }
            //Check MPN
            if (!string.IsNullOrEmpty(_mpn))
            {
                query = query + " And [MPN] = '" + _mpn + "'";
            }
            //Check Lotno
            if (!string.IsNullOrEmpty(_lotno))
            {
                query = query + " And [LotNo] = '" + _lotno + "'";
            }
            //Check User
            if (!string.IsNullOrEmpty(_username))
            {
                query = query + " And [UserRequest] = '" + _username + "'";
            }

            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }
        public bool IsSet_Finish(string _status, string _result, string _requestno, int _index)
        {
            var paramester = new object[] { _status, _result, UserProvider.Instance.DataUser.Username, _requestno, _index };
            string query = "Update [LAB_Details] Set [ItemStatus] = @ItemStatus ,[Result] = @Result ,[TimeFinish] = GETDATE(), [UserFinish] = @Username Where [RequestNo] = @RequestNo And [Index] = @Index ;";
            var _res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, paramester);
            UpdateStatusResult(_requestno);
            return _res > 0;
        }

        public void UpdateStatusResult(string _requestno)
        {
            string query = "SELECT [Result] FROM [LAB_Details] WHERE RequestNo = '" + _requestno + "'";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            if (data != null && data.Rows.Count > 0)
            {

                int Pass_Count = 0;
                int Fail_Count = 0;
                int NA_Count = 0;

                foreach (DataRow item in data.Rows)
                {
                    var itemResult = item["Result"].ToString();

                    if (string.IsNullOrEmpty(itemResult))
                    {
                        DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, "Update [LAB_Results] Set [Status] = 'On-Going',[Result] = Null Where [RequestNo] = '" + _requestno + "'");
                        return;
                    }
                    else
                    {
                        switch (itemResult)
                        {
                            case "N/A":
                                NA_Count++;
                                break;
                            case "Pass":
                                Pass_Count++;
                                break;
                            case "Fail":
                                Fail_Count++;
                                break;
                        }
                    }
                }
                string _result = "OK";
                string _status = "Censorship";

                if (Fail_Count > 0) _result = "NG";
                else if (Pass_Count > 0) _result = "OK";
                else if (NA_Count > 0)
                {
                    _result = "NA";
                    _status = "Reject";
                }
                else
                {
                    _result = "ERROR";
                    _status = "ERROR";
                }

                DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, $"Update [LAB_Results] Set [Status] = '{_status}',[Result] = '{_result}' Where [RequestNo] = '{_requestno}'");
            }
        }
        #endregion

        #region Censorship

        public bool Labdetail_NoteEdit(long serialID, string note, int errorqty)
        {
            string query = $"Update LAB_Details Set Notes = N'{note}', [ErrorQuantity] = {errorqty} Where SerialID = {serialID}";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        public bool Labdetail_NoteEdit(long serialID, string note)
        {
            string query = $"Update LAB_Details Set Notes = N'{note}' Where SerialID = {serialID}";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        public DataTable Data_Cencorship(string _Department, string _RequestNo = null, string _LotNo = null, string _UserName = null)
        {
            object[] parameter = new object[1];
            parameter[0] = _Department;
            int i = 1;
            string query = "Select * From LAB_Results Where [Status] in ('Censorship','On-Going') And [Department] = @Department ";

            if (!string.IsNullOrEmpty(_RequestNo))
            {
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _RequestNo;
                query = query + " And [RequestNo] = @RequestNo";
            }
            if (!string.IsNullOrEmpty(_LotNo))
            {
                query = query + " And [Lotno] = @Lotno ";
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _LotNo;
            }
            if (!string.IsNullOrEmpty(_UserName))
            {
                query = query + " And [UserRequest] = @UserName ";
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _UserName;
            }

            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return data;
        }

        public bool Exe_CensorshipResult(string _RequestNo, string _Result, string _UserName)
        {
            var paramester = new object[] { _Result, _UserName, _RequestNo };
            string query = "Update[LAB_Results] Set [Status] = 'Complete', [Result] = @Result , [TimeCensorship] = GetDate(), [UserCensorship] = @UserName Where [RequestNo] = @RequestNo ";
            var _res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, paramester);
            return _res > 0;
        }
        #endregion

        #region Search

        public DataTable Get_TableResult(string _condition, object[] parameter = null)
        {
            string query = "Select * From [LAB_Results] With(NoLock) Where " + _condition;
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
        }

        #endregion
    }
}
