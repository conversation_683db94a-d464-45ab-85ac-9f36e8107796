﻿#pragma checksum "..\..\..\View\AdministratorMMCV.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "0D49539FE04FB5E89F6934C90686710BCA246F7FC56F4BFD45B86C6632036567"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.View;


namespace SystemMMCV.View {
    
    
    /// <summary>
    /// AdministratorMMCV
    /// </summary>
    public partial class AdministratorMMCV : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 25 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox user_username;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox user_isadmin;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox user_fullname;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox user_male;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox user_department;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_user;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox fun_functionID;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox fun_functionName;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox fun_groupName;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox fun_describe;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid Dtg_Function;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox per_functionID;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox per_username;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox per_isAccept;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox per_roles;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\View\AdministratorMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid Dtg_Permission;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/view/administratormmcv.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\View\AdministratorMMCV.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.user_username = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.user_isadmin = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 3:
            this.user_fullname = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.user_male = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.user_department = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            
            #line 33 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_AddUser);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 34 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_EditUser);
            
            #line default
            #line hidden
            return;
            case 8:
            this.dtg_user = ((System.Windows.Controls.DataGrid)(target));
            
            #line 38 "..\..\..\View\AdministratorMMCV.xaml"
            this.dtg_user.AutoGeneratingColumn += new System.EventHandler<System.Windows.Controls.DataGridAutoGeneratingColumnEventArgs>(this.Event_HideColumn);
            
            #line default
            #line hidden
            
            #line 38 "..\..\..\View\AdministratorMMCV.xaml"
            this.dtg_user.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.Event_LoadUser);
            
            #line default
            #line hidden
            return;
            case 10:
            this.fun_functionID = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.fun_functionName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.fun_groupName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.fun_describe = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            
            #line 66 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_AddFunc);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 67 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_EditFunc);
            
            #line default
            #line hidden
            return;
            case 16:
            this.Dtg_Function = ((System.Windows.Controls.DataGrid)(target));
            
            #line 71 "..\..\..\View\AdministratorMMCV.xaml"
            this.Dtg_Function.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.Event_GetItemFunction);
            
            #line default
            #line hidden
            return;
            case 18:
            this.per_functionID = ((System.Windows.Controls.ComboBox)(target));
            
            #line 101 "..\..\..\View\AdministratorMMCV.xaml"
            this.per_functionID.DropDownClosed += new System.EventHandler(this.Event_SelectChange);
            
            #line default
            #line hidden
            return;
            case 19:
            this.per_username = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.per_isAccept = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.per_roles = ((System.Windows.Controls.TextBox)(target));
            return;
            case 22:
            
            #line 107 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_AddPermission);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 108 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_EditPermission);
            
            #line default
            #line hidden
            return;
            case 24:
            this.Dtg_Permission = ((System.Windows.Controls.DataGrid)(target));
            
            #line 112 "..\..\..\View\AdministratorMMCV.xaml"
            this.Dtg_Permission.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.Event_GetPersionItem);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 9:
            
            #line 43 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_deleteUser);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 82 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_DeleteFunc);
            
            #line default
            #line hidden
            break;
            case 25:
            
            #line 122 "..\..\..\View\AdministratorMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_DeletePermission);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

