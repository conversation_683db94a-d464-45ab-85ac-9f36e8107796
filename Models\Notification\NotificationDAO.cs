﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.Notification
{
    public class NotificationDAO
    {
        public DataTable Table_Notification()
        {
            string query = "Select Top(1000) * From MMCV_Notification With(NoLock) order by SerialID desc";
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }

        public DataTable Notification_GetStatus()
        {
            string query = "Select Top(1) * From MMCV_Notification With(NoLock) Where [Status] = 1 order by SerialID desc";
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }
    }
}
