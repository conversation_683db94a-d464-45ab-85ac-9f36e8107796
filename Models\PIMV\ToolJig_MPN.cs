﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    class ToolJig_MPN
    {
    }
    public class ToolJig_MPN_EXC
    {
        public static DataTable Get_DataJIG_MPN(out string exception, string mpn= null)
        {
            string query = "Select * from [ToolJig_MPN]";
            if (!string.IsNullOrEmpty(mpn)) query += $" Where MPN = '{mpn}';";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC48_PIMV, query);
            return data;
        }

        public static bool Add_LinkMPN(out string exception, string shortened, string mpn)
        {
            var isCheck = Exist_LinkMPN(out exception, shortened, mpn);
            if (isCheck == null)
            {
                return false;
            }
            else if (isCheck == false)
            {
                string query = "Insert Into [ToolJig_MPN] ([JIG_Shortened],[MPN]) Values ( @JIG_Shortened , @MPN );";
                var parameter = new object[] { shortened, mpn };
                var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC48_PIMV, query, parameter);
                return res > 0;
            }
            else
            {
                exception = "Cặp JIG_Shortened - MPN đã tồn tại.";
                return false;
            }
        }

        public static bool Edit_LinkMPN(out string exception, string shortened, string mpn, int serialId)
        {
            var isCheck = Exist_LinkMPN(out exception, shortened, mpn);
            if (isCheck == null)
            {
                return false;
            }
            else if(isCheck == false)
            {
                string query = "Update [ToolJig_MPN] Set [JIG_Shortened] = @JIG_Shortened ,[MPN] = @MPN Where [SerialID] = @SerialID ;";
                var parameter = new object[] { shortened, mpn, serialId };
                var res = ModelsProvider.SqlInstance.ExecuteNonQuery(out exception, SqlProvider.SqlSV.OFC48_PIMV, query, parameter);
                return res > 0;
            }
            else
            {
                exception = "Cặp JIG_Shortened - MPN đã tồn tại.";
                return false;
            }
        }

        public static bool? Exist_LinkMPN(out string exception, string shortened, string mpn)
        {
            try
            {
                string query = "Select count(*) from [ToolJig_MPN] Where [JIG_Shortened] = @JIG_Shortened And [MPN] = @MPN ;";
                var paramter = new object[] { shortened, mpn };
                var res = ModelsProvider.SqlInstance.ExecuteScalar(out exception, SqlProvider.SqlSV.OFC48_PIMV, query, paramter);
                if (res == null)
                {
                    return null;
                }
                else
                {
                    return (int)res > 0;
                }
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return null;
            }
        }
    }
}
