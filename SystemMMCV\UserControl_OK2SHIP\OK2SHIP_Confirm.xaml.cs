using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
//using System.Windows.Shapes;
using Microsoft.Win32;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using SystemMMCV.ViewModel;
using Excel = Microsoft.Office.Interop.Excel;
using System.Drawing;
using DocumentFormat.OpenXml.Wordprocessing;

namespace SystemMMCV.UserControl_OK2SHIP
{
    /// <summary>
    /// Interaction logic for OK2SHIP_Confirm.xaml
    /// </summary>   
    public partial class OK2SHIP_Confirm : UserControl
    {
        public OK2SHIP_Confirm()
        {
            InitializeComponent();
            GetModelsFromTbMaster();
            Load_Build();
            Load_Config();
            Load_OK2Ship_Request();
        }

        private void Event_Search(object sender, RoutedEventArgs e)
        {
            Load_OK2Ship_Request();
        }

        private void Event_Submit(object sender, RoutedEventArgs e)
        {
            var typeSubmit = grb_header.Header.ToString();
            switch (typeSubmit)
            {
                case "Add new":
                    AddNewItem();
                    break;
                case "Edit item":
                    EditItem();
                    break;
                default:
                    MessageBox.Show("Không xác định được typeSubmit.(Header cần là Add new hoặc Edit item)");
                    break;
            }
        }

        private void Event_ShowAdd(object sender, RoutedEventArgs e)
        {
            grb_header.Header = "Add new";
            EmptyFormInputValue();
            dwh_form.IsRightDrawerOpen = true;
        }
        private void Event_Download(object sender, RoutedEventArgs e)
        {
            if (dtg_results.SelectedItem is DataRowView rv)
            {
                int ok2hipid = (int)rv.Row["OK2ShipId"];
                string filename = rv.Row["FileName"].ToString();
                if (!string.IsNullOrEmpty(filename))
                {
                    string folder = ConfigurationManager.AppSettings["OK2Ship_Folder"];
                    string path = System.IO.Path.Combine(folder, ok2hipid.ToString(), filename);
                    DownloadFiles(path);
                }
                else
                {
                    MessageBox.Show("Không tìm thấy file Ok2Ship.");
                }
            }
        }

        private void Event_ShowEdit(object sender, RoutedEventArgs e)
        {
            if (dtg_results.SelectedItem is DataRowView row)
            {
                txt_id.Text = row.Row["OK2ShipId"].ToString();
                txt_model.Text = row.Row["Model"].ToString();
                txt_model.IsEnabled = false;
                txt_build.Text = row.Row["Build"].ToString();
                txt_config.Text = row.Row["Config"].ToString();
                txt_lotno.Text = row.Row["Lotno"].ToString();
                txt_lotno.IsEnabled = false;
                txt_program.Text = row.Row["ProgramName"].ToString();
                grb_header.Header = "Edit item";
                dwh_form.IsRightDrawerOpen = true;
            }
            else
            {
                MessageBox.Show("Hãy chọn item cần sửa rồi nhấn Edit.");
            }
        }

        #region MyRegion
        void Load_Build()
        {
            string exception;
            var _build = Models.PIMV.OK2Ship_Results_EXC.Get_Builds(out exception);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            cbb_build.ItemsSource = _build;
        }

        void Load_Config()
        {
            string exception;
            var _config = Models.PIMV.OK2Ship_Results_EXC.Get_Config(out exception);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            cbb_config.ItemsSource = _config;
        }
        void GetModelsFromTbMaster()
        {
            string exception;
            var _models = Models.PIMV.MMCV_Models_EXC.Get_Models(out exception);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            cbb_modelOk2ship.ItemsSource = _models;
            txt_model.ItemsSource = _models;
        }
        void Load_OK2Ship_Request()
        {
            string lotno = search_lotno.Text.Trim();
            string model = cbb_modelOk2ship.Text;
            string build = cbb_build.Text;
            string config = cbb_config.Text;
            var data = Models.PIMV.OK2Ship_Results_EXC.Get_OK2Ship_Results(out string exception, lotno, model, build, config);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            dtg_results.ItemsSource = data.DefaultView;
        }
        void EmptyFormInputValue()
        {
            txt_id.Text = null;
            txt_model.Text = null;
            txt_build.Text = null;
            txt_config.Text = null;
            txt_lotno.Text = null;
            txt_program.Text = null;
        }

        void AddNewItem()
        {
            if (CheckValueInput())
            {
                string model = txt_model.Text;
                string build = txt_build.Text;
                string config = txt_config.Text;
                string lotno = txt_lotno.Text;
                string program = txt_program.Text;
                string creator = Models.UserProvider.Instance.DataUser.Username;
                var res = Models.PIMV.OK2Ship_Results_EXC.Add_OK2Ship_Results(out string exception, model, build, config, lotno, program, creator);
                if (string.IsNullOrEmpty(exception))
                {
                    if (res)
                    {
                        dwh_form.IsRightDrawerOpen = false;
                        EmptyFormInputValue();
                        Load_OK2Ship_Request();
                    }
                    else
                    {
                        MessageBox.Show($"ERROR: Không thành công, hãy kiểm tra{Environment.NewLine}1. Cài đặt master cho Model{Environment.NewLine}2. [PIMV].[USP_OK2Ship_Resulst_Request]");
                    }
                }
                else
                {
                    MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("Bạn chưa nhập vào đủ thông tin.", "NOTIFICATION");

            }
        }

        void EditItem()
        {
            if (CheckValueInput(true))
            {
                int id = int.Parse(txt_id.Text);
                string model = txt_model.Text;
                string build = txt_build.Text;
                string config = txt_config.Text;
                string lotno = txt_lotno.Text;
                string program = txt_program.Text;
                string creator = Models.UserProvider.Instance.DataUser.Username;

                var res = Models.PIMV.OK2Ship_Results_EXC.Update_OK2Ship_Results(out string exception, model, build, config, lotno, program, creator, id);
                if (string.IsNullOrEmpty(exception))
                {
                    if (res)
                    {
                        dwh_form.IsRightDrawerOpen = false;
                        EmptyFormInputValue();
                        Load_OK2Ship_Request();
                    }
                    else
                    {
                        MessageBox.Show("Không thể thêm cập nhật dữ liệu vào bảng [**********].[PIMV].[OK2Ship_Result]");
                    }
                }
                else
                {
                    MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("Bạn chưa nhập vào đủ thông tin.", "NOTIFICATION");
            }
        }

        bool CheckValueInput(bool IsEdit = false)
        {
            if (string.IsNullOrEmpty(txt_model.Text)) return false;
            if (string.IsNullOrEmpty(txt_build.Text)) return false;
            if (string.IsNullOrEmpty(txt_config.Text)) return false;
            if (string.IsNullOrEmpty(txt_lotno.Text)) return false;
            if (IsEdit) if (string.IsNullOrEmpty(txt_id.Text)) return false;
            return true;
        }

        #endregion

        //private async void Event_ConfirmOK2Ship(object sender, RoutedEventArgs e)
        //{
        //    try
        //    {
        //        int ok2shipId = int.Parse(txt_SerialId.Text);
        //        string program = txt_Program.Text;
        //        string model = txt_Model.Text;
        //        string build = txt_Build.Text;
        //        string config = txt_Config.Text;
        //        string request = txt_RequetNo.Text;
        //        var listExtendStart = new List<string>();
        //        var listExtendEnd = new List<string>();
        //        //Declaration
        //        if (string.IsNullOrEmpty(txt_declaration.Text))
        //        {
        //            MessageBox.Show("Enter value declaration");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_declaration.Text);
        //        //Table of content
        //        if (string.IsNullOrEmpty(txt_tableOfContents.Text))
        //        {
        //            MessageBox.Show("Enter value table of contens");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_tableOfContents.Text);

        //        //Deviation
        //        if (string.IsNullOrEmpty(txt_Deviation.Text))
        //        {
        //            MessageBox.Show("Enter value process flow");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_Deviation.Text);

        //        //Yeild
        //        if (string.IsNullOrEmpty(txt_yield.Text))
        //        {
        //            MessageBox.Show("Enter value yield");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_yield.Text);
        //        //QC test
        //        if (string.IsNullOrEmpty( txt_qctest.Text))
        //        {
        //            MessageBox.Show("Enter value txt_qctest");
        //            return;
        //        }
        //        else listExtendEnd.Add(txt_qctest.Text);
        //        //Packing
        //        if (string.IsNullOrEmpty(txt_paking.Text))
        //        {
        //            MessageBox.Show("Enter value packing");
        //            return;
        //        }
        //        else listExtendEnd.Add(txt_paking.Text);
        //        //Process flow
        //        if (string.IsNullOrEmpty(txt_processFlow.Text))
        //        {
        //            MessageBox.Show("Enter value process flow");
        //            return;
        //        }
        //        else listExtendEnd.Add(txt_processFlow.Text);



        //        if (!string.IsNullOrEmpty(request))
        //        {
        //            //Cần sửa lại phần confirm
        //            var qs = MessageBox.Show($"Bạn đang xác nhận cho OK2SHIP với thông tin{Environment.NewLine}{program}_{model}_{build}_{config}.{Environment.NewLine}Nhấn YES để tiếp tục, NO để hủy bỏ.",
        //            "CONFIRM OK2SHIP", MessageBoxButton.YesNo, MessageBoxImage.Question);
        //            if (qs == MessageBoxResult.Yes)
        //            {
        //                string new_filename = $"Ok2ship Request Mektec VietNam Flex {program} {model} {build} {config} {DateTime.Now.ToString("yyyyMMdd")}.xlsx";
        //                dlh_process.IsOpen = true;
        //                await Task.Run(() => CreateFileOK2Ship(request, ok2shipId, new_filename, listExtendStart, listExtendEnd));
        //                dlh_process.IsOpen = false;
        //                Load_OK2Ship_Request();
        //            }
        //        }
        //        else
        //        {
        //            MessageBox.Show("RequestNo không hợp lệ.");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show(ex.Message);
        //    }
        //}

        //void CreateFileOK2Ship(string request, int ok2shipId, string new_filename, List<string> listExtendStart, List<string> listExtendEnd)
        //{
        //    //var data = Models.PIMV.OK2Ship_Details_EXC.Get_ItemByOK2ShipId(out string exception, ok2shipId);
        //    string exception = string.Empty;
        //    var data = Models.LAB.LAB_Details_EXC.Get_TableDetailsFinish(out exception, request);
        //    if (string.IsNullOrEmpty(exception))
        //    {
        //        if (data != null && data.Rows.Count > 0)
        //        {
        //            try
        //            {
        //                string master_folder = ConfigurationManager.AppSettings["OK2Ship_Master"];
        //                if (Directory.Exists(master_folder))
        //                {
        //                    var listMaster = Directory.GetFiles(master_folder, "*.xlsx").ToList();
        //                    if (listMaster.Count > 0)
        //                    {
        //                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        //                        using (var thisworkbook = new ExcelPackage())
        //                        {
        //                            //Add danh sách sht master
        //                            foreach (var master in listMaster)
        //                            {
        //                                //Mở file nguồn
        //                                using (var workbook = new ExcelPackage(new FileInfo(master)))
        //                                {
        //                                    foreach (var shtMaster in workbook.Workbook.Worksheets)
        //                                    {
        //                                        // Tạo bản sao của sheet trong workbook mới
        //                                        thisworkbook.Workbook.Worksheets.Add(shtMaster.Name, workbook.Workbook.Worksheets[shtMaster.Name]);
        //                                    }
        //                                    workbook.Dispose();
        //                                }
        //                            }

        //                            //Add danh sách sht listExtend
        //                            foreach (var excelFile in listExtendStart)
        //                            {
        //                                //Mở file nguồn
        //                                using (var workbook = new ExcelPackage(new FileInfo(excelFile)))
        //                                {
        //                                    foreach (var shtCopy in workbook.Workbook.Worksheets)
        //                                    {
        //                                        // Tạo bản sao của sheet trong workbook mới
        //                                        thisworkbook.Workbook.Worksheets.Add(shtCopy.Name, workbook.Workbook.Worksheets[shtCopy.Name]);
        //                                    }
        //                                    workbook.Dispose();
        //                                }
        //                            }

        //                            ServiceProvider.ConnectToFolderServer(out exception);
        //                            ///Folder lưu dữ liệu của PVS
        //                            var DIC_InfoFininsh = new Dictionary<string, DateTime>();
        //                            var pathLAB = ConfigurationManager.AppSettings["LAB_Folder"];
        //                            foreach (DataRow item in data.Rows)
        //                            {
        //                                string requestno = item["RequestNo"].ToString();
        //                                string folder = item["Items"].ToString();
        //                                if (!DIC_InfoFininsh.ContainsKey(folder))
        //                                {
        //                                    var timeFinish = (DateTime)item["TimeFinish"];
        //                                    DIC_InfoFininsh.Add(folder, timeFinish);
        //                                }

        //                                //string sheetname = item["SheetName"].ToString();
        //                                string sheetname = folder;
        //                                var path = System.IO.Path.Combine(pathLAB, requestno, folder);
        //                                ///Lấy danh sách file nguồn
        //                                var filenames = Directory.GetFiles(path);
        //                                ///Đọc file excel nguồn
        //                                bool isExitsName = false;
        //                                foreach (var file in filenames)
        //                                {
        //                                    //Mở file nguồn
        //                                    using (var workbook = new ExcelPackage(new FileInfo(file)))
        //                                    {
        //                                        //var worksheets = workbook.Workbook.Worksheets.ToList();

        //                                        //Kiểm  tra xem file có sheet cần lấy không:
        //                                        if (workbook.Workbook.Worksheets.Any(ws => ws.Name == sheetname))
        //                                        {
        //                                            // Tạo bản sao của sheet trong workbook mới
        //                                            var sourceSheet = workbook.Workbook.Worksheets[sheetname];
        //                                            try
        //                                            {
        //                                                thisworkbook.Workbook.Worksheets.Add(sheetname, sourceSheet);
        //                                                thisworkbook.Workbook.Worksheets[sheetname].View.TabSelected = true;
        //                                            }
        //                                            catch (Exception ex)
        //                                            {
        //                                                MessageBox.Show($"Can't copy sheet {sheetname} of file\n{file}\nException:\n{ex.Message}");
        //                                            }
        //                                            sourceSheet = null;

        //                                            isExitsName = true;
        //                                        }
        //                                    }
        //                                    if (isExitsName) break;
        //                                }
        //                            }

        //                            //Add danh sách sht listExtend
        //                            foreach (var excelFile in listExtendEnd)
        //                            {
        //                                //Mở file nguồn
        //                                using (var workbook = new ExcelPackage(new FileInfo(excelFile)))
        //                                {
        //                                    foreach (var shtCopy in workbook.Workbook.Worksheets)
        //                                    {
        //                                        // Tạo bản sao của sheet trong workbook mới
        //                                        string shtCopyName = shtCopy.Name;
        //                                        if (!thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == shtCopyName))
        //                                        {
        //                                            thisworkbook.Workbook.Worksheets.Add(shtCopy.Name, workbook.Workbook.Worksheets[shtCopy.Name]);
        //                                        }

        //                                    }
        //                                    workbook.Dispose();
        //                                }
        //                            }

        //                            //Đi count sheet in table of content
        //                            if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "Table of Contents"))
        //                            {
        //                                var shtTableOfContent = thisworkbook.Workbook.Worksheets["Table of Contents"];
        //                                //Add data vào cho shtmaster
        //                                if (shtTableOfContent != null)
        //                                {
        //                                    int int_r = 17;
        //                                    while (!string.IsNullOrEmpty(shtTableOfContent.Cells[int_r, 2].Text))
        //                                    {
        //                                        string key = shtTableOfContent.Cells[int_r, 10].Text.Trim();
        //                                        if (DIC_InfoFininsh.ContainsKey(key))
        //                                        {
        //                                            shtTableOfContent.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
        //                                        }
        //                                        int_r++;
        //                                    }
        //                                }
        //                            }
        //                            //Đi count QC Test
        //                            if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "OQC test"))
        //                            {
        //                                var shtQcTest = thisworkbook.Workbook.Worksheets["OQC Test"];
        //                                //Add data vào cho shtmaster
        //                                if (shtQcTest != null)
        //                                {
        //                                    int int_r = 2;
        //                                    while (!string.IsNullOrEmpty(shtQcTest.Cells[int_r, 2].Text))
        //                                    {
        //                                        string key = shtQcTest.Cells[int_r, 10].Text.Trim();
        //                                        if (DIC_InfoFininsh.ContainsKey(key))
        //                                        {
        //                                            shtQcTest.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
        //                                            shtQcTest.Cells[int_r, 7].Value = DIC_InfoFininsh[key];
        //                                        }
        //                                        int_r++;
        //                                    }
        //                                }
        //                            }

        //                            string folderServer = ConfigurationManager.AppSettings["OK2Ship_Folder"];
        //                            string new_folder = System.IO.Path.Combine(folderServer, ok2shipId.ToString());
        //                            string new_path = System.IO.Path.Combine(new_folder, new_filename);
        //                            //new_path = @"D:\LogFile\DA48-ABCBA2-QC5.0.xlsx";
        //                            if (!Directory.Exists(new_folder)) Directory.CreateDirectory(new_folder);
        //                            thisworkbook.SaveAs(new_path);
        //                            thisworkbook.Dispose();
        //                            //Thoát server
        //                            ServiceProvider.DisconnectToFolderServer(out exception);
        //                            var res = Models.PIMV.OK2Ship_Results_EXC.Update_OK2Ship_Confirm(out exception, new_filename, "OK", ok2shipId);
        //                            if (res)
        //                            {
        //                                MessageBox.Show("Hoàn thành tổng hợp OK2Ship");
        //                            }
        //                            else
        //                            {
        //                                MessageBox.Show($"Exception: {exception}");
        //                            }
        //                        }
        //                    }
        //                    else
        //                    {
        //                        MessageBox.Show("No find master OK2Ship. Pls check!", "MMCV");
        //                    }
        //                }

        //                //Tạo file lưu OK2Ship

        //            }
        //            catch (Exception ex)
        //            {
        //                MessageBox.Show(ex.Message, "ERROR");
        //            }
        //        }
        //        else
        //        {
        //            MessageBox.Show($"OK2SHIP không lấy được data trong OK2SHIP_Details với Ok2Ship_Id là {ok2shipId}", "ERROR");
        //        }
        //    }
        //    else
        //    {
        //        MessageBox.Show(exception, "ERROR");
        //    }
        //}
        private async void Event_ConfirmOK2Ship(object sender, RoutedEventArgs e)
        {
            try
            {
                int ok2shipId = int.Parse(txt_SerialId.Text);
                string program = txt_Program.Text;
                string model = txt_Model.Text;
                string build = txt_Build.Text;
                string config = txt_Config.Text;
                string request = txt_RequetNo.Text;
                var listExtend = new List<string>();
                //var listExtendEnd = new List<string>();

                //Table of content
                if (string.IsNullOrEmpty(txt_tableOfContents.Text))
                {
                    MessageBox.Show("Enter value table of contens");
                    return;
                }
                else listExtend.Add(txt_tableOfContents.Text);

                if (!string.IsNullOrEmpty(request))
                {
                    //Cần sửa lại phần confirm
                    var qs = MessageBox.Show($"Bạn đang xác nhận cho OK2SHIP với thông tin{Environment.NewLine}{program}_{model}_{build}_{config}.{Environment.NewLine}Nhấn YES để tiếp tục, NO để hủy bỏ.",
                    "CONFIRM OK2SHIP", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (qs == MessageBoxResult.Yes)
                    {
                        string new_filename = $"Ok2ship Request Mektec VietNam Flex {program} {model} {build} {config} {DateTime.Now.ToString("yyyyMMdd")}.xlsx";
                        dlh_process.IsOpen = true;
                        await Task.Run(() => CreateFileOK2Ship(request, ok2shipId, new_filename, listExtend));
                        dlh_process.IsOpen = false;
                        Load_OK2Ship_Request();
                    }
                }
                else
                {
                    MessageBox.Show("RequestNo không hợp lệ.");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void CreateFileOK2Ship(string request, int ok2shipId, string new_filename, List<string> listExtend)
        {
            //var data = Models.PIMV.OK2Ship_Details_EXC.Get_ItemByOK2ShipId(out string exception, ok2shipId);
            string exception = string.Empty;
            var data = Models.LAB.LAB_Details_EXC.Get_TableDetailsFinish(out exception, request);
            if (string.IsNullOrEmpty(exception))
            {
                if (data != null && data.Rows.Count > 0)
                {
                    try
                    {
                        string master_folder = ConfigurationManager.AppSettings["OK2Ship_Master"];
                        if (Directory.Exists(master_folder))
                        {
                            var listMaster = Directory.GetFiles(master_folder, "OK2SHIPQAUPDATE.xlsx").ToList();
                            //var listMaster = Directory.GetFiles(master_folder, "OK2ShipMaster.xlsx").ToList();
                            //var listMaster = listExtend.ToList();
                            if (listMaster.Count > 0)
                            {
                                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                                using (var thisworkbook = new ExcelPackage())
                                {
                                    //Add danh sách sht master
                                    foreach (var master in listMaster)
                                    {
                                        // Mở file nguồn
                                        using (var workbook = new ExcelPackage(new FileInfo(master)))
                                        {
                                            // Lấy tối đa 8 sheet đầu tiên
                                            var maxSheets = Math.Min(8, workbook.Workbook.Worksheets.Count);

                                            for (int i = 0; i < maxSheets; i++)
                                            {
                                                var shtMaster = workbook.Workbook.Worksheets[i];

                                                // Tạo bản sao của sheet trong workbook mới
                                                thisworkbook.Workbook.Worksheets.Add(shtMaster.Name, shtMaster);
                                              

                                            }
                                            workbook.Dispose();
                                        }
                                    }

                                    ServiceProvider.ConnectToFolderServer(out exception);
                                    ///Folder lưu dữ liệu của PVS
                                    var DIC_InfoFininsh = new Dictionary<string, DateTime>();
                                    var pathLAB = ConfigurationManager.AppSettings["LAB_Folder"];
                                    foreach (DataRow item in data.Rows)
                                    {
                                        string requestno = item["RequestNo"].ToString();
                                        string folder = item["Items"].ToString();
                                        if (!DIC_InfoFininsh.ContainsKey(folder))
                                        {
                                            var timeFinish = (DateTime)item["TimeFinish"];
                                            DIC_InfoFininsh.Add(folder, timeFinish);
                                        }

                                        //string sheetname = item["SheetName"].ToString();
                                        string sheetname = folder;
                                        var path = System.IO.Path.Combine(pathLAB, requestno, folder.Trim());
                                        ///Lấy danh sách file nguồn
                                        var filenames = Directory.GetFiles(path);
                                        ///Đọc file excel nguồn
                                        bool isExitsName = false;
                                        foreach (var file in filenames)
                                        {
                                            //Mở file nguồn
                                            using (var workbook = new ExcelPackage(new FileInfo(file)))
                                            {

                                                var worksheets = workbook.Workbook.Worksheets.ToList();
                                                foreach (var sheet in workbook.Workbook.Worksheets)
                                                {
                                                    sheet.Name = sheet.Name.Trim();
                                                }

                                                //Kiểm tra xem file có sheet cần lấy không:
                                                if (workbook.Workbook.Worksheets.Any(ws => ws.Name.Trim() == sheetname.Trim()))
                                                {
                                                    // Tạo bản sao của sheet trong workbook mới
                                                    var sourceSheet = workbook.Workbook.Worksheets[sheetname.Trim()];
                                                    try
                                                    {
                                                        //if (thisworkbook.Workbook.ThemeManager.CurrentTheme == null)
                                                        //{
                                                            var themeXml = workbook.Workbook.ThemeManager?.CurrentTheme?.ThemeXml;
                                                            if (themeXml != null)
                                                                thisworkbook.Workbook.ThemeManager.Load(themeXml);
                                                        //}

                                                        thisworkbook.Workbook.Worksheets.Add(sheetname, sourceSheet);
                                                      
                                                        thisworkbook.Workbook.Worksheets[sheetname].View.TabSelected = true;
                                                    }
                                                    catch (Exception ex)
                                                    {
                                                        MessageBox.Show($"Can't copy sheet {sheetname} of file\n{file}\nException:\n{ex.Message}");
                                                    }
                                                    sourceSheet = null;

                                                    isExitsName = true;
                                                }
                                            }
                                            if (isExitsName) break;
                                        }
                                    }

                                    foreach (var master in listMaster)
                                    {
                                        // Mở file nguồn
                                        using (var workbook = new ExcelPackage(new FileInfo(master)))
                                        {
                                            var sheets = workbook.Workbook.Worksheets;
                                            int totalSheets = sheets.Count;

                                            // Lấy từ sheet có index: totalSheets - 4 đến totalSheets - 1
                                            int startIndex = Math.Max(0, totalSheets - 4); // Đảm bảo không âm

                                            for (int i = startIndex; i < totalSheets; i++)
                                            {
                                                var sht = sheets[i];
                                                thisworkbook.Workbook.Worksheets.Add(sht.Name, sht);
                                                
                                            }
                                            workbook.Dispose();
                                        }
                                    }

                                    //Đi count sheet in table of content
                                    if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "Table of Contents"))
                                    {
                                        var shtTableOfContent = thisworkbook.Workbook.Worksheets["Table of Contents"];
                                        //Add data vào cho shtmaster
                                        if (shtTableOfContent != null)
                                        {
                                            int int_r = 17;
                                            while (!string.IsNullOrEmpty(shtTableOfContent.Cells[int_r, 2].Text))
                                            {
                                                string key = shtTableOfContent.Cells[int_r, 10].Text.Trim();
                                                if (DIC_InfoFininsh.ContainsKey(key))
                                                {
                                                    shtTableOfContent.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
                                                }
                                                int_r++;
                                            }
                                        }
                                    }

                                    //Đi count QC Test
                                    if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "OQC test"))
                                    {
                                        var shtQcTest = thisworkbook.Workbook.Worksheets["OQC Test"];
                                        //Add data vào cho shtmaster
                                        if (shtQcTest != null)
                                        {
                                            int int_r = 2;
                                            while (!string.IsNullOrEmpty(shtQcTest.Cells[int_r, 2].Text))
                                            {
                                                string key = shtQcTest.Cells[int_r, 10].Text.Trim();
                                                if (DIC_InfoFininsh.ContainsKey(key))
                                                {
                                                    shtQcTest.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
                                                    shtQcTest.Cells[int_r, 7].Value = DIC_InfoFininsh[key];
                                                }
                                                int_r++;
                                            }
                                        }
                                    }


                                    //Đi count B2B Mating Un-mating test
                                    if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "B2B Mating Un-mating test"))
                                    {
                                        var shtQcTest = thisworkbook.Workbook.Worksheets["B2B Mating Un-mating test"];
                                        //Add data vào cho shtmaster
                                        if (shtQcTest != null)
                                        {
                                            int int_r = 2;
                                            while (!string.IsNullOrEmpty(shtQcTest.Cells[int_r, 2].Text))
                                            {
                                                string key = shtQcTest.Cells[int_r, 10].Text.Trim();
                                                if (DIC_InfoFininsh.ContainsKey(key))
                                                {
                                                    shtQcTest.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
                                                    shtQcTest.Cells[int_r, 7].Value = DIC_InfoFininsh[key];
                                                }
                                                int_r++;
                                            }
                                        }
                                    }


                                    //Đi count ORT-Assy
                                    if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "ORT-Assy"))
                                    {
                                        var shtQcTest = thisworkbook.Workbook.Worksheets["ORT-Assy"];
                                        //Add data vào cho shtmaster
                                        if (shtQcTest != null)
                                        {
                                            int int_r = 2;
                                            while (!string.IsNullOrEmpty(shtQcTest.Cells[int_r, 2].Text))
                                            {
                                                string key = shtQcTest.Cells[int_r, 10].Text.Trim();
                                                if (DIC_InfoFininsh.ContainsKey(key))
                                                {
                                                    shtQcTest.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
                                                    shtQcTest.Cells[int_r, 7].Value = DIC_InfoFininsh[key];
                                                }
                                                int_r++;
                                            }
                                        }
                                    }

                                    string folderServer = ConfigurationManager.AppSettings["OK2Ship_Folder"];
                                    string new_folder = System.IO.Path.Combine(folderServer, ok2shipId.ToString());
                                    string new_path = System.IO.Path.Combine(new_folder, new_filename);
                                    //new_path = @"D:\LogFile\DA48-ABCBA2-QC5.0.xlsx";
                                    if (!Directory.Exists(new_folder)) Directory.CreateDirectory(new_folder);
                                    thisworkbook.SaveAs(new_path);
                                    thisworkbook.Dispose();
                                    //Thoát server
                                    ServiceProvider.DisconnectToFolderServer(out exception);
                                    var res = Models.PIMV.OK2Ship_Results_EXC.Update_OK2Ship_Confirm(out exception, new_filename, "OK", ok2shipId);
                                    if (res)
                                    {
                                        MessageBox.Show("Hoàn thành tổng hợp OK2Ship");
                                    }
                                    else
                                    {
                                        MessageBox.Show($"Exception: {exception}");
                                    }
                                }
                            }
                            else
                            {
                                MessageBox.Show("No find master OK2Ship. Pls check!", "MMCV");
                            }
                        }
                        //Tạo file lưu OK2Ship
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.Message, "ERROR");
                    }
                }
                else
                {
                    MessageBox.Show($"OK2SHIP không lấy được data trong OK2SHIP_Details với Ok2Ship_Id là {ok2shipId}", "ERROR");
                }
            }
            else
            {
                MessageBox.Show(exception, "ERROR");
            }
        }




        void DownloadFiles(string file)
        {
            string _newFolder = @"C:\Users\<USER>\Downloads\MMCV_SYS";
            if (!Directory.Exists(_newFolder))
            {
                Directory.CreateDirectory(_newFolder);
            }

            string fileName = System.IO.Path.GetFileName(file);
            string pathNew = System.IO.Path.Combine(_newFolder, fileName);
            try
            {
                File.Copy(file, pathNew, true);
                System.Diagnostics.Process.Start(pathNew, "MMCV_SYSTEM");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void Event_Refresh(object sender, RoutedEventArgs e)
        {
            GetModelsFromTbMaster();
            Load_Build();
            Load_Config();
            Load_OK2Ship_Request();
        }

        private void Show_ConfirmOK2Ship(object sender, RoutedEventArgs e)
        {
            if (dtg_results.SelectedItem is DataRowView rowview)
            {
                txt_SerialId.Text = rowview.Row["OK2ShipId"].ToString();
                txt_Program.Text = rowview.Row["ProgramName"].ToString();
                txt_Model.Text = rowview.Row["Model"].ToString();
                txt_Build.Text = rowview.Row["Build"].ToString();
                txt_Config.Text = rowview.Row["Config"].ToString();
                txt_RequetNo.Text = rowview.Row["RequestNo"].ToString();
                drh_confirm.IsRightDrawerOpen = true;
            }
        }

        private void PackIcon_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (sender is MaterialDesignThemes.Wpf.PackIcon icon)
            {
                try
                {
                    string nameTbx = icon.Tag.ToString();
                    TextBox textBox = (TextBox)this.FindName(nameTbx);
                    OpenFileDialog openFile = new OpenFileDialog();
                    openFile.Filter = "All file excel |*.xlsx";
                    openFile.Multiselect = false;
                    if (openFile.ShowDialog() == true)
                    {
                        string nameFile = openFile.FileName;
                        if (!string.IsNullOrEmpty(nameFile))
                        {
                            textBox.Text = nameFile;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }


            }
        }
    }
}
