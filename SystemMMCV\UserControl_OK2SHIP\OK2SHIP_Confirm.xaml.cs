using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
//using System.Windows.Shapes;
using Microsoft.Win32;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using SystemMMCV.ViewModel;
using Excel = Microsoft.Office.Interop.Excel;
using System.Drawing;
using DocumentFormat.OpenXml.Wordprocessing;

namespace SystemMMCV.UserControl_OK2SHIP
{
    /// <summary>
    /// Interaction logic for OK2SHIP_Confirm.xaml
    /// </summary>   
    public partial class OK2SHIP_Confirm : UserControl
    {
        public OK2SHIP_Confirm()
        {
            InitializeComponent();
            GetModelsFromTbMaster();
            Load_Build();
            Load_Config();
            Load_OK2Ship_Request();
        }

        private void Event_Search(object sender, RoutedEventArgs e)
        {
            Load_OK2Ship_Request();
        }

        private void Event_Submit(object sender, RoutedEventArgs e)
        {
            var typeSubmit = grb_header.Header.ToString();
            switch (typeSubmit)
            {
                case "Add new":
                    AddNewItem();
                    break;
                case "Edit item":
                    EditItem();
                    break;
                default:
                    MessageBox.Show("Không xác định được typeSubmit.(Header cần là Add new hoặc Edit item)");
                    break;
            }
        }

        private void Event_ShowAdd(object sender, RoutedEventArgs e)
        {
            grb_header.Header = "Add new";
            EmptyFormInputValue();
            dwh_form.IsRightDrawerOpen = true;
        }
        private void Event_Download(object sender, RoutedEventArgs e)
        {
            if (dtg_results.SelectedItem is DataRowView rv)
            {
                int ok2hipid = (int)rv.Row["OK2ShipId"];
                string filename = rv.Row["FileName"].ToString();
                if (!string.IsNullOrEmpty(filename))
                {
                    string folder = ConfigurationManager.AppSettings["OK2Ship_Folder"];
                    string path = System.IO.Path.Combine(folder, ok2hipid.ToString(), filename);
                    DownloadFiles(path);
                }
                else
                {
                    MessageBox.Show("Không tìm thấy file Ok2Ship.");
                }
            }
        }

        private void Event_ShowEdit(object sender, RoutedEventArgs e)
        {
            if (dtg_results.SelectedItem is DataRowView row)
            {
                txt_id.Text = row.Row["OK2ShipId"].ToString();
                txt_model.Text = row.Row["Model"].ToString();
                txt_model.IsEnabled = false;
                txt_build.Text = row.Row["Build"].ToString();
                txt_config.Text = row.Row["Config"].ToString();
                txt_lotno.Text = row.Row["Lotno"].ToString();
                txt_lotno.IsEnabled = false;
                txt_program.Text = row.Row["ProgramName"].ToString();
                grb_header.Header = "Edit item";
                dwh_form.IsRightDrawerOpen = true;
            }
            else
            {
                MessageBox.Show("Hãy chọn item cần sửa rồi nhấn Edit.");
            }
        }

        #region MyRegion
        void Load_Build()
        {
            string exception;
            var _build = Models.PIMV.OK2Ship_Results_EXC.Get_Builds(out exception);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            cbb_build.ItemsSource = _build;
        }

        void Load_Config()
        {
            string exception;
            var _config = Models.PIMV.OK2Ship_Results_EXC.Get_Config(out exception);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            cbb_config.ItemsSource = _config;
        }
        void GetModelsFromTbMaster()
        {
            string exception;
            var _models = Models.PIMV.MMCV_Models_EXC.Get_Models(out exception);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            cbb_modelOk2ship.ItemsSource = _models;
            txt_model.ItemsSource = _models;
        }
        void Load_OK2Ship_Request()
        {
            string lotno = search_lotno.Text.Trim();
            string model = cbb_modelOk2ship.Text;
            string build = cbb_build.Text;
            string config = cbb_config.Text;
            var data = Models.PIMV.OK2Ship_Results_EXC.Get_OK2Ship_Results(out string exception, lotno, model, build, config);
            if (!string.IsNullOrEmpty(exception)) MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
            dtg_results.ItemsSource = data.DefaultView;
        }
        void EmptyFormInputValue()
        {
            txt_id.Text = null;
            txt_model.Text = null;
            txt_build.Text = null;
            txt_config.Text = null;
            txt_lotno.Text = null;
            txt_program.Text = null;
        }

        void AddNewItem()
        {
            if (CheckValueInput())
            {
                string model = txt_model.Text;
                string build = txt_build.Text;
                string config = txt_config.Text;
                string lotno = txt_lotno.Text;
                string program = txt_program.Text;
                string creator = Models.UserProvider.Instance.DataUser.Username;
                var res = Models.PIMV.OK2Ship_Results_EXC.Add_OK2Ship_Results(out string exception, model, build, config, lotno, program, creator);
                if (string.IsNullOrEmpty(exception))
                {
                    if (res)
                    {
                        dwh_form.IsRightDrawerOpen = false;
                        EmptyFormInputValue();
                        Load_OK2Ship_Request();
                    }
                    else
                    {
                        MessageBox.Show($"ERROR: Không thành công, hãy kiểm tra{Environment.NewLine}1. Cài đặt master cho Model{Environment.NewLine}2. [PIMV].[USP_OK2Ship_Resulst_Request]");
                    }
                }
                else
                {
                    MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("Bạn chưa nhập vào đủ thông tin.", "NOTIFICATION");

            }
        }

        void EditItem()
        {
            if (CheckValueInput(true))
            {
                int id = int.Parse(txt_id.Text);
                string model = txt_model.Text;
                string build = txt_build.Text;
                string config = txt_config.Text;
                string lotno = txt_lotno.Text;
                string program = txt_program.Text;
                string creator = Models.UserProvider.Instance.DataUser.Username;

                var res = Models.PIMV.OK2Ship_Results_EXC.Update_OK2Ship_Results(out string exception, model, build, config, lotno, program, creator, id);
                if (string.IsNullOrEmpty(exception))
                {
                    if (res)
                    {
                        dwh_form.IsRightDrawerOpen = false;
                        EmptyFormInputValue();
                        Load_OK2Ship_Request();
                    }
                    else
                    {
                        MessageBox.Show("Không thể thêm cập nhật dữ liệu vào bảng [**********].[PIMV].[OK2Ship_Result]");
                    }
                }
                else
                {
                    MessageBox.Show(exception, "ERROR!", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("Bạn chưa nhập vào đủ thông tin.", "NOTIFICATION");
            }
        }

        bool CheckValueInput(bool IsEdit = false)
        {
            if (string.IsNullOrEmpty(txt_model.Text)) return false;
            if (string.IsNullOrEmpty(txt_build.Text)) return false;
            if (string.IsNullOrEmpty(txt_config.Text)) return false;
            if (string.IsNullOrEmpty(txt_lotno.Text)) return false;
            if (IsEdit) if (string.IsNullOrEmpty(txt_id.Text)) return false;
            return true;
        }

        #endregion

        //private async void Event_ConfirmOK2Ship(object sender, RoutedEventArgs e)
        //{
        //    try
        //    {
        //        int ok2shipId = int.Parse(txt_SerialId.Text);
        //        string program = txt_Program.Text;
        //        string model = txt_Model.Text;
        //        string build = txt_Build.Text;
        //        string config = txt_Config.Text;
        //        string request = txt_RequetNo.Text;
        //        var listExtendStart = new List<string>();
        //        var listExtendEnd = new List<string>();
        //        //Declaration
        //        if (string.IsNullOrEmpty(txt_declaration.Text))
        //        {
        //            MessageBox.Show("Enter value declaration");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_declaration.Text);
        //        //Table of content
        //        if (string.IsNullOrEmpty(txt_tableOfContents.Text))
        //        {
        //            MessageBox.Show("Enter value table of contens");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_tableOfContents.Text);

        //        //Deviation
        //        if (string.IsNullOrEmpty(txt_Deviation.Text))
        //        {
        //            MessageBox.Show("Enter value process flow");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_Deviation.Text);

        //        //Yeild
        //        if (string.IsNullOrEmpty(txt_yield.Text))
        //        {
        //            MessageBox.Show("Enter value yield");
        //            return;
        //        }
        //        else listExtendStart.Add(txt_yield.Text);
        //        //QC test
        //        if (string.IsNullOrEmpty( txt_qctest.Text))
        //        {
        //            MessageBox.Show("Enter value txt_qctest");
        //            return;
        //        }
        //        else listExtendEnd.Add(txt_qctest.Text);
        //        //Packing
        //        if (string.IsNullOrEmpty(txt_paking.Text))
        //        {
        //            MessageBox.Show("Enter value packing");
        //            return;
        //        }
        //        else listExtendEnd.Add(txt_paking.Text);
        //        //Process flow
        //        if (string.IsNullOrEmpty(txt_processFlow.Text))
        //        {
        //            MessageBox.Show("Enter value process flow");
        //            return;
        //        }
        //        else listExtendEnd.Add(txt_processFlow.Text);



        //        if (!string.IsNullOrEmpty(request))
        //        {
        //            //Cần sửa lại phần confirm
        //            var qs = MessageBox.Show($"Bạn đang xác nhận cho OK2SHIP với thông tin{Environment.NewLine}{program}_{model}_{build}_{config}.{Environment.NewLine}Nhấn YES để tiếp tục, NO để hủy bỏ.",
        //            "CONFIRM OK2SHIP", MessageBoxButton.YesNo, MessageBoxImage.Question);
        //            if (qs == MessageBoxResult.Yes)
        //            {
        //                string new_filename = $"Ok2ship Request Mektec VietNam Flex {program} {model} {build} {config} {DateTime.Now.ToString("yyyyMMdd")}.xlsx";
        //                dlh_process.IsOpen = true;
        //                await Task.Run(() => CreateFileOK2Ship(request, ok2shipId, new_filename, listExtendStart, listExtendEnd));
        //                dlh_process.IsOpen = false;
        //                Load_OK2Ship_Request();
        //            }
        //        }
        //        else
        //        {
        //            MessageBox.Show("RequestNo không hợp lệ.");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show(ex.Message);
        //    }
        //}

        //void CreateFileOK2Ship(string request, int ok2shipId, string new_filename, List<string> listExtendStart, List<string> listExtendEnd)
        //{
        //    //var data = Models.PIMV.OK2Ship_Details_EXC.Get_ItemByOK2ShipId(out string exception, ok2shipId);
        //    string exception = string.Empty;
        //    var data = Models.LAB.LAB_Details_EXC.Get_TableDetailsFinish(out exception, request);
        //    if (string.IsNullOrEmpty(exception))
        //    {
        //        if (data != null && data.Rows.Count > 0)
        //        {
        //            try
        //            {
        //                string master_folder = ConfigurationManager.AppSettings["OK2Ship_Master"];
        //                if (Directory.Exists(master_folder))
        //                {
        //                    var listMaster = Directory.GetFiles(master_folder, "*.xlsx").ToList();
        //                    if (listMaster.Count > 0)
        //                    {
        //                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        //                        using (var thisworkbook = new ExcelPackage())
        //                        {
        //                            //Add danh sách sht master
        //                            foreach (var master in listMaster)
        //                            {
        //                                //Mở file nguồn
        //                                using (var workbook = new ExcelPackage(new FileInfo(master)))
        //                                {
        //                                    foreach (var shtMaster in workbook.Workbook.Worksheets)
        //                                    {
        //                                        // Tạo bản sao của sheet trong workbook mới
        //                                        thisworkbook.Workbook.Worksheets.Add(shtMaster.Name, workbook.Workbook.Worksheets[shtMaster.Name]);
        //                                    }
        //                                    workbook.Dispose();
        //                                }
        //                            }

        //                            //Add danh sách sht listExtend
        //                            foreach (var excelFile in listExtendStart)
        //                            {
        //                                //Mở file nguồn
        //                                using (var workbook = new ExcelPackage(new FileInfo(excelFile)))
        //                                {
        //                                    foreach (var shtCopy in workbook.Workbook.Worksheets)
        //                                    {
        //                                        // Tạo bản sao của sheet trong workbook mới
        //                                        thisworkbook.Workbook.Worksheets.Add(shtCopy.Name, workbook.Workbook.Worksheets[shtCopy.Name]);
        //                                    }
        //                                    workbook.Dispose();
        //                                }
        //                            }

        //                            ServiceProvider.ConnectToFolderServer(out exception);
        //                            ///Folder lưu dữ liệu của PVS
        //                            var DIC_InfoFininsh = new Dictionary<string, DateTime>();
        //                            var pathLAB = ConfigurationManager.AppSettings["LAB_Folder"];
        //                            foreach (DataRow item in data.Rows)
        //                            {
        //                                string requestno = item["RequestNo"].ToString();
        //                                string folder = item["Items"].ToString();
        //                                if (!DIC_InfoFininsh.ContainsKey(folder))
        //                                {
        //                                    var timeFinish = (DateTime)item["TimeFinish"];
        //                                    DIC_InfoFininsh.Add(folder, timeFinish);
        //                                }

        //                                //string sheetname = item["SheetName"].ToString();
        //                                string sheetname = folder;
        //                                var path = System.IO.Path.Combine(pathLAB, requestno, folder);
        //                                ///Lấy danh sách file nguồn
        //                                var filenames = Directory.GetFiles(path);
        //                                ///Đọc file excel nguồn
        //                                bool isExitsName = false;
        //                                foreach (var file in filenames)
        //                                {
        //                                    //Mở file nguồn
        //                                    using (var workbook = new ExcelPackage(new FileInfo(file)))
        //                                    {
        //                                        //var worksheets = workbook.Workbook.Worksheets.ToList();

        //                                        //Kiểm  tra xem file có sheet cần lấy không:
        //                                        if (workbook.Workbook.Worksheets.Any(ws => ws.Name == sheetname))
        //                                        {
        //                                            // Tạo bản sao của sheet trong workbook mới
        //                                            var sourceSheet = workbook.Workbook.Worksheets[sheetname];
        //                                            try
        //                                            {
        //                                                thisworkbook.Workbook.Worksheets.Add(sheetname, sourceSheet);
        //                                                thisworkbook.Workbook.Worksheets[sheetname].View.TabSelected = true;
        //                                            }
        //                                            catch (Exception ex)
        //                                            {
        //                                                MessageBox.Show($"Can't copy sheet {sheetname} of file\n{file}\nException:\n{ex.Message}");
        //                                            }
        //                                            sourceSheet = null;

        //                                            isExitsName = true;
        //                                        }
        //                                    }
        //                                    if (isExitsName) break;
        //                                }
        //                            }

        //                            //Add danh sách sht listExtend
        //                            foreach (var excelFile in listExtendEnd)
        //                            {
        //                                //Mở file nguồn
        //                                using (var workbook = new ExcelPackage(new FileInfo(excelFile)))
        //                                {
        //                                    foreach (var shtCopy in workbook.Workbook.Worksheets)
        //                                    {
        //                                        // Tạo bản sao của sheet trong workbook mới
        //                                        string shtCopyName = shtCopy.Name;
        //                                        if (!thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == shtCopyName))
        //                                        {
        //                                            thisworkbook.Workbook.Worksheets.Add(shtCopy.Name, workbook.Workbook.Worksheets[shtCopy.Name]);
        //                                        }

        //                                    }
        //                                    workbook.Dispose();
        //                                }
        //                            }

        //                            //Đi count sheet in table of content
        //                            if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "Table of Contents"))
        //                            {
        //                                var shtTableOfContent = thisworkbook.Workbook.Worksheets["Table of Contents"];
        //                                //Add data vào cho shtmaster
        //                                if (shtTableOfContent != null)
        //                                {
        //                                    int int_r = 17;
        //                                    while (!string.IsNullOrEmpty(shtTableOfContent.Cells[int_r, 2].Text))
        //                                    {
        //                                        string key = shtTableOfContent.Cells[int_r, 10].Text.Trim();
        //                                        if (DIC_InfoFininsh.ContainsKey(key))
        //                                        {
        //                                            shtTableOfContent.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
        //                                        }
        //                                        int_r++;
        //                                    }
        //                                }
        //                            }
        //                            //Đi count QC Test
        //                            if (thisworkbook.Workbook.Worksheets.Any(ws => ws.Name == "OQC test"))
        //                            {
        //                                var shtQcTest = thisworkbook.Workbook.Worksheets["OQC Test"];
        //                                //Add data vào cho shtmaster
        //                                if (shtQcTest != null)
        //                                {
        //                                    int int_r = 2;
        //                                    while (!string.IsNullOrEmpty(shtQcTest.Cells[int_r, 2].Text))
        //                                    {
        //                                        string key = shtQcTest.Cells[int_r, 10].Text.Trim();
        //                                        if (DIC_InfoFininsh.ContainsKey(key))
        //                                        {
        //                                            shtQcTest.Cells[int_r, 8].Value = DIC_InfoFininsh[key];
        //                                            shtQcTest.Cells[int_r, 7].Value = DIC_InfoFininsh[key];
        //                                        }
        //                                        int_r++;
        //                                    }
        //                                }
        //                            }

        //                            string folderServer = ConfigurationManager.AppSettings["OK2Ship_Folder"];
        //                            string new_folder = System.IO.Path.Combine(folderServer, ok2shipId.ToString());
        //                            string new_path = System.IO.Path.Combine(new_folder, new_filename);
        //                            //new_path = @"D:\LogFile\DA48-ABCBA2-QC5.0.xlsx";
        //                            if (!Directory.Exists(new_folder)) Directory.CreateDirectory(new_folder);
        //                            thisworkbook.SaveAs(new_path);
        //                            thisworkbook.Dispose();
        //                            //Thoát server
        //                            ServiceProvider.DisconnectToFolderServer(out exception);
        //                            var res = Models.PIMV.OK2Ship_Results_EXC.Update_OK2Ship_Confirm(out exception, new_filename, "OK", ok2shipId);
        //                            if (res)
        //                            {
        //                                MessageBox.Show("Hoàn thành tổng hợp OK2Ship");
        //                            }
        //                            else
        //                            {
        //                                MessageBox.Show($"Exception: {exception}");
        //                            }
        //                        }
        //                    }
        //                    else
        //                    {
        //                        MessageBox.Show("No find master OK2Ship. Pls check!", "MMCV");
        //                    }
        //                }

        //                //Tạo file lưu OK2Ship

        //            }
        //            catch (Exception ex)
        //            {
        //                MessageBox.Show(ex.Message, "ERROR");
        //            }
        //        }
        //        else
        //        {
        //            MessageBox.Show($"OK2SHIP không lấy được data trong OK2SHIP_Details với Ok2Ship_Id là {ok2shipId}", "ERROR");
        //        }
        //    }
        //    else
        //    {
        //        MessageBox.Show(exception, "ERROR");
        //    }
        //}
        private async void Event_ConfirmOK2Ship(object sender, RoutedEventArgs e)
        {
            try
            {
                int ok2shipId = int.Parse(txt_SerialId.Text);
                string program = txt_Program.Text;
                string model = txt_Model.Text;
                string build = txt_Build.Text;
                string config = txt_Config.Text;
                string request = txt_RequetNo.Text;
                var listExtend = new List<string>();
                //var listExtendEnd = new List<string>();

                //Table of content
                if (string.IsNullOrEmpty(txt_tableOfContents.Text))
                {
                    MessageBox.Show("Enter value table of contens");
                    return;
                }
                else listExtend.Add(txt_tableOfContents.Text);

                if (!string.IsNullOrEmpty(request))
                {
                    //Cần sửa lại phần confirm
                    var qs = MessageBox.Show($"Bạn đang xác nhận cho OK2SHIP với thông tin{Environment.NewLine}{program}_{model}_{build}_{config}.{Environment.NewLine}Nhấn YES để tiếp tục, NO để hủy bỏ.",
                    "CONFIRM OK2SHIP", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (qs == MessageBoxResult.Yes)
                    {
                        string new_filename = $"Ok2ship Request Mektec VietNam Flex {program} {model} {build} {config} {DateTime.Now.ToString("yyyyMMdd")}.xlsx";
                        dlh_process.IsOpen = true;
                        await Task.Run(() => CreateFileOK2Ship(request, ok2shipId, new_filename, listExtend));
                        dlh_process.IsOpen = false;
                        Load_OK2Ship_Request();
                    }
                }
                else
                {
                    MessageBox.Show("RequestNo không hợp lệ.");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void CreateFileOK2Ship(string request, int ok2shipId, string new_filename, List<string> listExtend)
        {
            string exception = string.Empty;
            var data = Models.LAB.LAB_Details_EXC.Get_TableDetailsFinish(out exception, request);
            if (string.IsNullOrEmpty(exception))
            {
                if (data != null && data.Rows.Count > 0)
                {
                    try
                    {
                        string master_folder = ConfigurationManager.AppSettings["OK2Ship_Master"];
                        if (Directory.Exists(master_folder))
                        {
                            var listMaster = Directory.GetFiles(master_folder, "OK2SHIPQAUPDATE.xlsx").ToList();
                            if (listMaster.Count > 0)
                            {
                                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                                // Thử approach mới: Copy file master gốc và thay thế dữ liệu
                                bool useNewApproach = true;

                                if (useNewApproach)
                                {
                                    System.Diagnostics.Debug.WriteLine("=== SỬ DỤNG APPROACH MỚI: COPY FILE GỐC ===");
                                    CreateFileOK2ShipNewApproach(listMaster, data, DIC_InfoFinish, ok2shipId, new_filename, listExtend);
                                    return;
                                }

                                using (var thisworkbook = new ExcelPackage())
                                {
                                    // Track các sheet đã được thêm để tránh duplicate
                                    var addedSheets = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                                    System.Diagnostics.Debug.WriteLine("=== BẮT ĐẦU TẠO FILE OK2SHIP ===");

                                    // Bước 1: Thêm các sheet master đầu tiên (8 sheet đầu)
                                    System.Diagnostics.Debug.WriteLine("BƯỚC 1: Copy 8 sheet master đầu");
                                    foreach (var master in listMaster)
                                    {
                                        using (var workbook = new ExcelPackage(new FileInfo(master)))
                                        {
                                            var maxSheets = Math.Min(8, workbook.Workbook.Worksheets.Count);
                                            for (int i = 0; i < maxSheets; i++)
                                            {
                                                var shtMaster = workbook.Workbook.Worksheets[i];
                                                try
                                                {
                                                    // Kiểm tra xem sheet đã được thêm chưa
                                                    if (!addedSheets.Contains(shtMaster.Name))
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"[MASTER] Bắt đầu copy sheet: {shtMaster.Name}");

                                                        // Kiểm tra source sheet có dữ liệu không
                                                        if (shtMaster.Dimension != null)
                                                        {
                                                            System.Diagnostics.Debug.WriteLine($"[MASTER] Sheet {shtMaster.Name} có dữ liệu: {shtMaster.Dimension.Address}");
                                                        }
                                                        else
                                                        {
                                                            System.Diagnostics.Debug.WriteLine($"[MASTER] Sheet {shtMaster.Name} KHÔNG có dữ liệu");
                                                        }

                                                        // Copy theme và styles từ workbook gốc
                                                        CopyWorkbookThemeAndStyles(workbook, thisworkbook);

                                                        // Copy sheet với format đầy đủ
                                                        CopySheetWithFormat(shtMaster, thisworkbook, shtMaster.Name);

                                                        // Đánh dấu đã thêm
                                                        addedSheets.Add(shtMaster.Name);
                                                        System.Diagnostics.Debug.WriteLine($"[MASTER] Đã thêm sheet: {shtMaster.Name}");
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"[MASTER] Sheet {shtMaster.Name} đã tồn tại, bỏ qua");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"[MASTER] Lỗi copy sheet {shtMaster.Name}: {ex.Message}");
                                                    MessageBox.Show($"Lỗi khi copy sheet master {shtMaster.Name}: {ex.Message}");
                                                }
                                            }
                                        }
                                    }

                                    // Bước 2: Kết nối server và thu thập dữ liệu LAB
                                    System.Diagnostics.Debug.WriteLine("BƯỚC 2: Copy LAB data sheets");
                                    ServiceProvider.ConnectToFolderServer(out exception);
                                    var DIC_InfoFinish = new Dictionary<string, DateTime>();
                                    var pathLAB = ConfigurationManager.AppSettings["LAB_Folder"];

                                    foreach (DataRow item in data.Rows)
                                    {
                                        string requestno = item["RequestNo"].ToString();
                                        string folder = item["Items"].ToString().Trim();

                                        // Thu thập thông tin thời gian hoàn thành
                                        if (!DIC_InfoFinish.ContainsKey(folder))
                                        {
                                            var timeFinish = (DateTime)item["TimeFinish"];
                                            DIC_InfoFinish.Add(folder, timeFinish);
                                        }

                                        string sheetname = folder;
                                        var path = System.IO.Path.Combine(pathLAB, requestno, folder);

                                        if (!Directory.Exists(path))
                                        {
                                            MessageBox.Show($"Không tìm thấy thư mục: {path}");
                                            continue;
                                        }

                                        var filenames = Directory.GetFiles(path, "*.xlsx");
                                        bool isSheetFound = false;

                                        foreach (var file in filenames)
                                        {
                                            try
                                            {
                                                using (var workbook = new ExcelPackage(new FileInfo(file)))
                                                {
                                                    // Chuẩn hóa tên sheet
                                                    foreach (var sheet in workbook.Workbook.Worksheets)
                                                    {
                                                        sheet.Name = sheet.Name.Trim();
                                                    }

                                                    // Tìm sheet cần thiết
                                                    var targetSheet = workbook.Workbook.Worksheets
                                                        .FirstOrDefault(ws => ws.Name.Equals(sheetname.Trim(), StringComparison.OrdinalIgnoreCase));

                                                    if (targetSheet != null)
                                                    {
                                                        try
                                                        {
                                                            // Kiểm tra xem sheet đã được thêm chưa
                                                            if (!addedSheets.Contains(sheetname.Trim()))
                                                            {
                                                                System.Diagnostics.Debug.WriteLine($"[LAB] Bắt đầu copy sheet: {sheetname.Trim()}");

                                                                // Copy theme và styles từ workbook gốc
                                                                CopyWorkbookThemeAndStyles(workbook, thisworkbook);

                                                                // Copy sheet với format đầy đủ
                                                                CopySheetWithFormat(targetSheet, thisworkbook, sheetname.Trim());

                                                                // Đánh dấu đã thêm
                                                                addedSheets.Add(sheetname.Trim());
                                                                System.Diagnostics.Debug.WriteLine($"[LAB] Đã thêm sheet: {sheetname.Trim()}");
                                                            }
                                                            else
                                                            {
                                                                System.Diagnostics.Debug.WriteLine($"[LAB] Sheet {sheetname.Trim()} đã tồn tại, bỏ qua");
                                                            }

                                                            isSheetFound = true;
                                                        }
                                                        catch (Exception ex)
                                                        {
                                                            MessageBox.Show($"Lỗi khi copy sheet {sheetname.Trim()} từ file {file}:\n{ex.Message}");
                                                        }
                                                    }
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                MessageBox.Show($"Lỗi khi đọc file {file}:\n{ex.Message}");
                                            }

                                            if (isSheetFound) break;
                                        }

                                        if (!isSheetFound)
                                        {
                                            MessageBox.Show($"Không tìm thấy sheet '{sheetname.Trim()}' trong thư mục {path}");
                                        }
                                    }

                                    // Bước 3: Thêm các sheet master cuối (4 sheet cuối)
                                    System.Diagnostics.Debug.WriteLine("BƯỚC 3: Copy 4 sheet master cuối");
                                    foreach (var master in listMaster)
                                    {
                                        using (var workbook = new ExcelPackage(new FileInfo(master)))
                                        {
                                            var sheets = workbook.Workbook.Worksheets;
                                            int totalSheets = sheets.Count;
                                            int startIndex = Math.Max(0, totalSheets - 4);

                                            for (int i = startIndex; i < totalSheets; i++)
                                            {
                                                var sht = sheets[i];
                                                try
                                                {
                                                    // Kiểm tra xem sheet đã được thêm chưa
                                                    if (!addedSheets.Contains(sht.Name))
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"[MASTER-END] Bắt đầu copy sheet: {sht.Name}");

                                                        // Copy theme và styles từ workbook gốc
                                                        CopyWorkbookThemeAndStyles(workbook, thisworkbook);

                                                        // Copy sheet với format đầy đủ
                                                        CopySheetWithFormat(sht, thisworkbook, sht.Name);

                                                        // Đánh dấu đã thêm
                                                        addedSheets.Add(sht.Name);
                                                        System.Diagnostics.Debug.WriteLine($"[MASTER-END] Đã thêm sheet: {sht.Name}");
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"[MASTER-END] Sheet {sht.Name} đã tồn tại, bỏ qua");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"[MASTER-END] Lỗi copy sheet {sht.Name}: {ex.Message}");
                                                    MessageBox.Show($"Lỗi khi thêm sheet cuối {sht.Name}: {ex.Message}");
                                                }
                                            }
                                        }
                                    }

                                    // Bước 4: Cập nhật dữ liệu vào các sheet đặc biệt
                                    UpdateTableOfContents(thisworkbook, DIC_InfoFinish);
                                    UpdateOQCTestSheet(thisworkbook, DIC_InfoFinish);
                                    UpdateB2BMatingSheet(thisworkbook, DIC_InfoFinish);
                                    UpdateORTAssySheet(thisworkbook, DIC_InfoFinish);

                                    // Bước 5: Summary và validation
                                    System.Diagnostics.Debug.WriteLine($"=== SUMMARY ===");
                                    System.Diagnostics.Debug.WriteLine($"Tổng số sheet đã thêm: {addedSheets.Count}");
                                    System.Diagnostics.Debug.WriteLine($"Danh sách sheet: {string.Join(", ", addedSheets)}");
                                    System.Diagnostics.Debug.WriteLine($"Số sheet trong workbook: {thisworkbook.Workbook.Worksheets.Count}");

                                    // Kiểm tra duplicate
                                    var actualSheets = thisworkbook.Workbook.Worksheets.Select(ws => ws.Name).ToList();
                                    var duplicates = actualSheets.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key);
                                    if (duplicates.Any())
                                    {
                                        System.Diagnostics.Debug.WriteLine($"WARNING: Phát hiện sheet duplicate: {string.Join(", ", duplicates)}");
                                    }

                                    // Bước 6: Lưu file và hoàn tất
                                    string folderServer = ConfigurationManager.AppSettings["OK2Ship_Folder"];
                                    string new_folder = System.IO.Path.Combine(folderServer, ok2shipId.ToString());
                                    string new_path = System.IO.Path.Combine(new_folder, new_filename);

                                    if (!Directory.Exists(new_folder))
                                        Directory.CreateDirectory(new_folder);

                                    // Force recalculation trước khi save
                                    ForceWorkbookRecalculation(thisworkbook);

                                    thisworkbook.SaveAs(new_path);
                                    System.Diagnostics.Debug.WriteLine($"=== ĐÃ LƯU FILE: {new_path} ===");

                                    // Ngắt kết nối server
                                    ServiceProvider.DisconnectToFolderServer(out exception);

                                    // Cập nhật database
                                    var res = Models.PIMV.OK2Ship_Results_EXC.Update_OK2Ship_Confirm(out exception, new_filename, "OK", ok2shipId);
                                    if (res)
                                    {
                                        MessageBox.Show("Hoàn thành tổng hợp OK2Ship", "Thành công", MessageBoxButton.OK, MessageBoxImage.Information);
                                    }
                                    else
                                    {
                                        MessageBox.Show($"Lỗi cập nhật database: {exception}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
                                    }
                                }
                            }
                            else
                            {
                                MessageBox.Show("Không tìm thấy file master OK2Ship. Vui lòng kiểm tra!", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Warning);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Lỗi trong quá trình tạo file OK2Ship:\n{ex.Message}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"Không lấy được dữ liệu LAB_Details với OK2Ship_Id: {ok2shipId}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show($"Lỗi kết nối hoặc truy vấn dữ liệu:\n{exception}", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region Helper Methods for Sheet Updates

        /// <summary>
        /// Cập nhật sheet "Table of Contents" với thông tin thời gian hoàn thành
        /// </summary>
        private void UpdateTableOfContents(ExcelPackage workbook, Dictionary<string, DateTime> finishInfo)
        {
            var sheet = workbook.Workbook.Worksheets.FirstOrDefault(ws => ws.Name.Equals("Table of Contents", StringComparison.OrdinalIgnoreCase));
            if (sheet != null)
            {
                try
                {
                    int row = 17;
                    while (!string.IsNullOrEmpty(sheet.Cells[row, 2].Text))
                    {
                        string key = sheet.Cells[row, 10].Text.Trim();
                        if (finishInfo.ContainsKey(key))
                        {
                            sheet.Cells[row, 8].Value = finishInfo[key];
                        }
                        row++;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi cập nhật Table of Contents: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Cập nhật sheet "OQC test" với thông tin thời gian hoàn thành
        /// </summary>
        private void UpdateOQCTestSheet(ExcelPackage workbook, Dictionary<string, DateTime> finishInfo)
        {
            var sheet = workbook.Workbook.Worksheets.FirstOrDefault(ws => ws.Name.Equals("OQC test", StringComparison.OrdinalIgnoreCase));
            if (sheet != null)
            {
                try
                {
                    int row = 2;
                    while (!string.IsNullOrEmpty(sheet.Cells[row, 2].Text))
                    {
                        string key = sheet.Cells[row, 10].Text.Trim();
                        if (finishInfo.ContainsKey(key))
                        {
                            sheet.Cells[row, 8].Value = finishInfo[key];
                            sheet.Cells[row, 7].Value = finishInfo[key];
                        }
                        row++;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi cập nhật OQC Test: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Cập nhật sheet "B2B Mating Un-mating test" với thông tin thời gian hoàn thành
        /// </summary>
        private void UpdateB2BMatingSheet(ExcelPackage workbook, Dictionary<string, DateTime> finishInfo)
        {
            var sheet = workbook.Workbook.Worksheets.FirstOrDefault(ws => ws.Name.Equals("B2B Mating Un-mating test", StringComparison.OrdinalIgnoreCase));
            if (sheet != null)
            {
                try
                {
                    int row = 2;
                    while (!string.IsNullOrEmpty(sheet.Cells[row, 2].Text))
                    {
                        string key = sheet.Cells[row, 10].Text.Trim();
                        if (finishInfo.ContainsKey(key))
                        {
                            sheet.Cells[row, 8].Value = finishInfo[key];
                            sheet.Cells[row, 7].Value = finishInfo[key];
                        }
                        row++;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi cập nhật B2B Mating: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Cập nhật sheet "ORT-Assy" với thông tin thời gian hoàn thành
        /// </summary>
        private void UpdateORTAssySheet(ExcelPackage workbook, Dictionary<string, DateTime> finishInfo)
        {
            var sheet = workbook.Workbook.Worksheets.FirstOrDefault(ws => ws.Name.Equals("ORT-Assy", StringComparison.OrdinalIgnoreCase));
            if (sheet != null)
            {
                try
                {
                    int row = 2;
                    while (!string.IsNullOrEmpty(sheet.Cells[row, 2].Text))
                    {
                        string key = sheet.Cells[row, 10].Text.Trim();
                        if (finishInfo.ContainsKey(key))
                        {
                            sheet.Cells[row, 8].Value = finishInfo[key];
                            sheet.Cells[row, 7].Value = finishInfo[key];
                        }
                        row++;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Lỗi cập nhật ORT-Assy: {ex.Message}");
                }
            }
        }

        #endregion

        void DownloadFiles(string file)
        {
            string _newFolder = @"C:\Users\<USER>\Downloads\MMCV_SYS";
            if (!Directory.Exists(_newFolder))
            {
                Directory.CreateDirectory(_newFolder);
            }

            string fileName = System.IO.Path.GetFileName(file);
            string pathNew = System.IO.Path.Combine(_newFolder, fileName);
            try
            {
                File.Copy(file, pathNew, true);
                System.Diagnostics.Process.Start(pathNew, "MMCV_SYSTEM");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// Approach mới: Copy file master gốc và thay thế dữ liệu để giữ nguyên format
        /// </summary>
        private void CreateFileOK2ShipNewApproach(List<string> listMaster, DataTable data, Dictionary<string, DateTime> DIC_InfoFinish, int ok2shipId, string new_filename, List<string> listExtend)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== BẮT ĐẦU NEW APPROACH ===");

                // Bước 1: Copy file master gốc làm base
                string masterFile = listMaster.FirstOrDefault();
                if (string.IsNullOrEmpty(masterFile) || !File.Exists(masterFile))
                {
                    System.Diagnostics.Debug.WriteLine("Không tìm thấy file master");
                    return;
                }

                string folderServer = ConfigurationManager.AppSettings["OK2Ship_Folder"];
                string new_folder = System.IO.Path.Combine(folderServer, ok2shipId.ToString());
                string new_path = System.IO.Path.Combine(new_folder, new_filename);

                if (!Directory.Exists(new_folder))
                    Directory.CreateDirectory(new_folder);

                // Copy file master làm base (giữ nguyên tất cả format)
                File.Copy(masterFile, new_path, true);
                System.Diagnostics.Debug.WriteLine($"Đã copy file master: {masterFile} -> {new_path}");

                // Bước 2: Mở file đã copy và thay thế/thêm dữ liệu LAB
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var workbook = new ExcelPackage(new FileInfo(new_path)))
                {
                    System.Diagnostics.Debug.WriteLine($"Đã mở file để chỉnh sửa: {new_path}");

                    // Bước 3: Thêm các sheet LAB data (giữ nguyên format từ file gốc)
                    AddLabDataSheetsToExistingWorkbook(workbook, data, listExtend);

                    // Bước 4: Cập nhật dữ liệu vào các sheet đặc biệt
                    UpdateTableOfContents(workbook, DIC_InfoFinish);
                    UpdateOQCTestSheet(workbook, DIC_InfoFinish);
                    UpdateB2BMatingSheet(workbook, DIC_InfoFinish);
                    UpdateORTAssySheet(workbook, DIC_InfoFinish);

                    // Bước 5: Lưu file
                    workbook.Save();
                    System.Diagnostics.Debug.WriteLine($"=== ĐÃ LƯU FILE NEW APPROACH: {new_path} ===");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Lỗi New Approach: {ex.Message}");
                MessageBox.Show($"Lỗi tạo file OK2Ship (New Approach): {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm các sheet LAB data vào workbook đã có sẵn
        /// </summary>
        private void AddLabDataSheetsToExistingWorkbook(ExcelPackage workbook, DataTable data, List<string> listExtend)
        {
            try
            {
                string exception = string.Empty;
                ServiceProvider.ConnectToFolderServer(out exception);
                var pathLAB = ConfigurationManager.AppSettings["LAB_Folder"];
                var addedSheets = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                // Track các sheet đã có sẵn trong file master
                foreach (var existingSheet in workbook.Workbook.Worksheets)
                {
                    addedSheets.Add(existingSheet.Name);
                    System.Diagnostics.Debug.WriteLine($"Sheet đã có sẵn: {existingSheet.Name}");
                }

                foreach (DataRow item in data.Rows)
                {
                    string requestno = item["RequestNo"].ToString();
                    string folder = item["Items"].ToString().Trim();
                    string sheetname = folder;
                    var path = System.IO.Path.Combine(pathLAB, requestno, folder);

                    if (!Directory.Exists(path))
                    {
                        System.Diagnostics.Debug.WriteLine($"Không tìm thấy thư mục LAB: {path}");
                        continue;
                    }

                    var files = Directory.GetFiles(path, "*.xlsx").Where(f => listExtend.Any(ext => f.Contains(ext))).ToList();
                    if (files.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"Không tìm thấy file Excel trong: {path}");
                        continue;
                    }

                    foreach (var file in files)
                    {
                        try
                        {
                            using (var labWorkbook = new ExcelPackage(new FileInfo(file)))
                            {
                                foreach (var labSheet in labWorkbook.Workbook.Worksheets)
                                {
                                    string normalizedName = labSheet.Name.Trim();
                                    if (normalizedName.Equals(sheetname.Trim(), StringComparison.OrdinalIgnoreCase))
                                    {
                                        if (!addedSheets.Contains(normalizedName))
                                        {
                                            System.Diagnostics.Debug.WriteLine($"[LAB] Thêm sheet: {normalizedName}");

                                            // Copy sheet từ LAB file (format sẽ được giữ nguyên)
                                            workbook.Workbook.Worksheets.Add(normalizedName, labSheet);
                                            addedSheets.Add(normalizedName);

                                            System.Diagnostics.Debug.WriteLine($"[LAB] Đã thêm sheet: {normalizedName}");
                                        }
                                        else
                                        {
                                            System.Diagnostics.Debug.WriteLine($"[LAB] Sheet {normalizedName} đã tồn tại, bỏ qua");
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Lỗi xử lý file LAB {file}: {ex.Message}");
                        }
                    }
                }

                ServiceProvider.DisconnectToFolderServer(out exception);
                System.Diagnostics.Debug.WriteLine($"Đã thêm tổng cộng {addedSheets.Count} sheets");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Lỗi AddLabDataSheetsToExistingWorkbook: {ex.Message}");
            }
        }

        /// <summary>
        /// Copy theme từ source workbook sang target workbook
        /// </summary>
        private void CopyWorkbookThemeAndStyles(ExcelPackage sourceWorkbook, ExcelPackage targetWorkbook)
        {
            try
            {
                // Copy theme
                var themeXml = sourceWorkbook.Workbook.ThemeManager?.CurrentTheme?.ThemeXml;
                if (themeXml != null && targetWorkbook.Workbook.ThemeManager.CurrentTheme == null)
                {
                    targetWorkbook.Workbook.ThemeManager.Load(themeXml);
                }

                // Bỏ qua copy named styles vì EPPlus có vấn đề với NamedStyles.Add()
                // Theme đã đủ để đảm bảo format cơ bản
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Lỗi copy workbook theme: {ex.Message}");
            }
        }

        /// <summary>
        /// Copy sheet với format đầy đủ sử dụng Excel Interop để đảm bảo format chính xác
        /// </summary>
        private void CopySheetWithFormat(ExcelWorksheet sourceSheet, ExcelPackage targetWorkbook, string targetSheetName)
        {
            try
            {
                // Kiểm tra xem sheet đã tồn tại chưa
                if (targetWorkbook.Workbook.Worksheets.Any(ws => ws.Name.Equals(targetSheetName, StringComparison.OrdinalIgnoreCase)))
                {
                    System.Diagnostics.Debug.WriteLine($"Sheet {targetSheetName} đã tồn tại, bỏ qua copy");
                    return;
                }

                // Kiểm tra source sheet có dữ liệu không
                if (sourceSheet.Dimension == null)
                {
                    System.Diagnostics.Debug.WriteLine($"Sheet {targetSheetName} không có dữ liệu, copy trực tiếp");
                    targetWorkbook.Workbook.Worksheets.Add(targetSheetName, sourceSheet);
                    return;
                }

                // Copy bằng EPPlus (approach cũ chỉ dùng khi cần)
                System.Diagnostics.Debug.WriteLine($"Copy sheet {targetSheetName} bằng EPPlus");
                var newSheet = targetWorkbook.Workbook.Worksheets.Add(targetSheetName, sourceSheet);
                newSheet.View.TabSelected = true;
                newSheet.Calculate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Lỗi copy sheet {targetSheetName}: {ex.Message}");

                // Final fallback
                try
                {
                    if (!targetWorkbook.Workbook.Worksheets.Any(ws => ws.Name.Equals(targetSheetName, StringComparison.OrdinalIgnoreCase)))
                    {
                        targetWorkbook.Workbook.Worksheets.Add(targetSheetName, sourceSheet);
                    }
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Final fallback failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Copy sheet sử dụng Excel Interop để đảm bảo format được copy chính xác
        /// </summary>
        private bool CopySheetWithExcelInterop(ExcelWorksheet sourceSheet, ExcelPackage targetWorkbook, string targetSheetName)
        {
            Excel.Application excelApp = null;
            Excel.Workbook sourceWorkbook = null;
            Excel.Workbook targetExcelWorkbook = null;

            try
            {
                System.Diagnostics.Debug.WriteLine($"Thử copy sheet {targetSheetName} bằng Excel Interop");

                // Tạo Excel Application
                excelApp = new Excel.Application();
                excelApp.Visible = false;
                excelApp.DisplayAlerts = false;

                // Tạo file tạm cho source
                string sourceTempFile = Path.GetTempFileName() + ".xlsx";
                using (var tempPackage = new ExcelPackage())
                {
                    tempPackage.Workbook.Worksheets.Add(sourceSheet.Name, sourceSheet);
                    tempPackage.SaveAs(new FileInfo(sourceTempFile));
                }

                // Tạo file tạm cho target
                string targetTempFile = Path.GetTempFileName() + ".xlsx";
                targetWorkbook.SaveAs(new FileInfo(targetTempFile));

                // Mở cả 2 file bằng Excel Interop
                sourceWorkbook = excelApp.Workbooks.Open(sourceTempFile);
                targetExcelWorkbook = excelApp.Workbooks.Open(targetTempFile);

                // Copy sheet từ source sang target
                Excel.Worksheet sourceExcelSheet = sourceWorkbook.Worksheets[1];
                sourceExcelSheet.Copy(After: targetExcelWorkbook.Worksheets[targetExcelWorkbook.Worksheets.Count]);

                // Đổi tên sheet
                Excel.Worksheet copiedSheet = targetExcelWorkbook.Worksheets[targetExcelWorkbook.Worksheets.Count];
                copiedSheet.Name = targetSheetName;

                // Lưu file target
                targetExcelWorkbook.Save();
                targetExcelWorkbook.Close();
                sourceWorkbook.Close();

                // Load lại vào EPPlus
                using (var fileStream = new FileStream(targetTempFile, FileMode.Open, FileAccess.Read))
                {
                    targetWorkbook.Load(fileStream);
                }

                // Cleanup
                File.Delete(sourceTempFile);
                File.Delete(targetTempFile);

                System.Diagnostics.Debug.WriteLine($"Copy sheet {targetSheetName} bằng Excel Interop thành công");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Lỗi copy bằng Excel Interop: {ex.Message}");
                return false;
            }
            finally
            {
                // Cleanup Excel objects
                try
                {
                    targetExcelWorkbook?.Close(false);
                    sourceWorkbook?.Close(false);
                    excelApp?.Quit();

                    if (targetExcelWorkbook != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(targetExcelWorkbook);
                    if (sourceWorkbook != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(sourceWorkbook);
                    if (excelApp != null) System.Runtime.InteropServices.Marshal.ReleaseComObject(excelApp);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }

        /// <summary>
        /// Force recalculation cho toàn bộ workbook để đảm bảo conditional formatting hoạt động
        /// </summary>
        private void ForceWorkbookRecalculation(ExcelPackage workbook)
        {
            try
            {
                // Set calculation mode
                workbook.Workbook.CalcMode = ExcelCalcMode.Automatic;

                // Force calculate tất cả worksheets
                foreach (var worksheet in workbook.Workbook.Worksheets)
                {
                    try
                    {
                        worksheet.Calculate();
                        System.Diagnostics.Debug.WriteLine($"Calculated worksheet: {worksheet.Name}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Lỗi calculate worksheet {worksheet.Name}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Lỗi force workbook recalculation: {ex.Message}");
            }
        }

        private void Event_Refresh(object sender, RoutedEventArgs e)
        {
            GetModelsFromTbMaster();
            Load_Build();
            Load_Config();
            Load_OK2Ship_Request();
        }

        private void Show_ConfirmOK2Ship(object sender, RoutedEventArgs e)
        {
            if (dtg_results.SelectedItem is DataRowView rowview)
            {
                txt_SerialId.Text = rowview.Row["OK2ShipId"].ToString();
                txt_Program.Text = rowview.Row["ProgramName"].ToString();
                txt_Model.Text = rowview.Row["Model"].ToString();
                txt_Build.Text = rowview.Row["Build"].ToString();
                txt_Config.Text = rowview.Row["Config"].ToString();
                txt_RequetNo.Text = rowview.Row["RequestNo"].ToString();
                drh_confirm.IsRightDrawerOpen = true;
            }
        }

        private void PackIcon_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (sender is MaterialDesignThemes.Wpf.PackIcon icon)
            {
                try
                {
                    string nameTbx = icon.Tag.ToString();
                    TextBox textBox = (TextBox)this.FindName(nameTbx);
                    OpenFileDialog openFile = new OpenFileDialog();
                    openFile.Filter = "All file excel |*.xlsx";
                    openFile.Multiselect = false;
                    if (openFile.ShowDialog() == true)
                    {
                        string nameFile = openFile.FileName;
                        if (!string.IsNullOrEmpty(nameFile))
                        {
                            textBox.Text = nameFile;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }


            }
        }
    }
}
