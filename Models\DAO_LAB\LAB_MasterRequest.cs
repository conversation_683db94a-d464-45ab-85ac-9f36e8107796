﻿using System;
using System.Data;

namespace Models.DAO_LAB
{
    public class LAB_MasterRequest
    {
        private int serialID;
        private string type;
        private string department;
        private string model;
        private int index;
        private string keyProcess;
        private string items;
        private string location;
        private int quantity;
        private string frequency;
        private string criteria;
        private string staffNo;
        private DateTime? enterTime;

        public string Department { get => department; set => department = value; }
        public string Model { get => model; set => model = value; }
        public int Index { get => index; set => index = value; }
        public string KeyProcess { get => keyProcess; set => keyProcess = value; }
        public string Items { get => items; set => items = value; }
        public string Location { get => location; set => location = value; }
        public int Quantity { get => quantity; set => quantity = value; }
        public string Frequency { get => frequency; set => frequency = value; }
        public string Criteria { get => criteria; set => criteria = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }
        public int SerialID { get => serialID; set => serialID = value; }
        public string Type { get => type; set => type = value; }

        public LAB_MasterRequest() { }
        public LAB_MasterRequest(DataRow row)
        {
            this.SerialID = (int)row["SerialID"];
            this.Type = row["Type"].ToString();
            this.Department = row["Department"].ToString();
            this.Model = row["Model"].ToString();
            this.Index = (int)row["Index"];
            this.KeyProcess = row["KeyProcess"].ToString();
            this.Items = row["Items"].ToString();
            this.Location = row["Location"].ToString();
            this.Quantity = (int)row["Quantity"];
            this.Frequency = row["Frequency"].ToString();
            this.Criteria = row["Criteria"].ToString();
            this.StaffNo = row["StaffNo"].ToString();
            this.EnterTime = (DateTime)row["EnterTime"];
        }
    }
}
