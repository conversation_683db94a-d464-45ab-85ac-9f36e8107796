﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class IQC_Results
    {
        private long iD;
        private string receivedDate;
        private string supplierCode;
        private string materialCode;
        private string invoiceNo;
        private string lOT;
        private string phase;
        private string tSC;
        private int quantity;
        private string unit;
        private string isPacking;
        private string isTicket;
        private string isData;
        private string expiryDate;
        private string supplierResult;
        private string sampleConfirm;
        private string requestNo;
        private string mektecResult;
        private string final_Judgement;
        private string userUpdate;
        private string enterTime;

        public long ID { get => iD; set => iD = value; }
        public string ReceivedDate { get => receivedDate; set => receivedDate = value; }
        public string SupplierCode { get => supplierCode; set => supplierCode = value; }
        public string MaterialCode { get => materialCode; set => materialCode = value; }
        public string InvoiceNo { get => invoiceNo; set => invoiceNo = value; }
        public string Phase { get => phase; set => phase = value; }
        public string LOT { get => lOT; set => lOT = value; }
        public string TSC { get => tSC; set => tSC = value; }
        public int Quantity { get => quantity; set => quantity = value; }
        public string Unit { get => unit; set => unit = value; }
        public string IsPacking { get => isPacking; set => isPacking = value; }
        public string IsTicket { get => isTicket; set => isTicket = value; }
        public string IsData { get => isData; set => isData = value; }
        public string ExpiryDate { get => expiryDate; set => expiryDate = value; }
        public string SupplierResult { get => supplierResult; set => supplierResult = value; }
        public string SampleConfirm { get => sampleConfirm; set => sampleConfirm = value; }
        public string RequestNo { get => requestNo; set => requestNo = value; }
        public string MektecResult { get => mektecResult; set => mektecResult = value; }
        public string Final_Judgement { get => final_Judgement; set => final_Judgement = value; }
        public string UserUpdate { get => userUpdate; set => userUpdate = value; }
        public string EnterTime { get => enterTime; set => enterTime = value; }

    }

    public class IQC_Results_EXC
    {        
        public static DataTable GET_DataCorrectIQC(out string exception, string mmcvLotno)
        {
            string query = $"Select A.*,B.Status,B.Result from IQC_Results A With(NoLock) Inner Join LAB_Results B With(NoLock) on A.RequestNo = B.RequestNo Where MMCVLotNo = '{mmcvLotno}';";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return data;
        }

        public static DataTable Get_MaterialInfo(out string exception,string materialCode)
        {
            string query = "SELECT [MaterialCode],[SupplierCode],TypeName,B.TypeID FROM [IQC_Materials] A with(NoLock) " +
                $"Inner Join IQC_TypeMaster B On A.TypeID = B.TypeID Where MaterialCode = '{materialCode}';";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return data;
        }
        public static DataTable GET_ResultsLAB(out string exception, DateTime d_start, DateTime d_end)
        {
            string str_start = d_start.ToString("yyyy-MM-dd");
            string str_end = d_end.ToString("yyyy-MM-dd");
            string query = "Select ReceivedDate,Customer,SupplierCode,[MaterialCode],Model,InvoiceNo,Phase,LOT,TSC,A.Quantity,Unit,IsPacking,IsTicket,IsData," +
                "ExpiryDate,SupplierResult,SampleConfirm,B.Result 'MektecResult',Shift,Line,ProdDate,Status,[Index],Items,C.Quantity,ErrorLimits,ErrorQuantity," +
                "Criteria,Notes,Performed,ListTime,TimeRequest,UserRequest,TimeConfirm,UserConfirm,TimeFinish,UserFinish,ItemStatus,C.Result " +
                "From IQC_Results A with(NoLock) Inner Join LAB_Results B with(NoLock) On A.RequestNo = B.RequestNo " +
                "Inner Join LAB_Details C with(NoLock) On A.RequestNo = C.RequestNo " +
                $"Where  A.ReceivedDate Between '{str_start}' And '{str_end}' Order by A.ID,[Index];";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            return data;
        }
        public static DataTable GET_Results(out String exception, string lot, string suppliercode, string materialcode,DateTime? d_start, DateTime? d_end)
        {
            try
            {
                string query = @"SELECT Top (5000) [ID],[ReceivedDate],[Customer],[SupplierCode],[MaterialCode],[Model],[Phase],[InvoiceNo],[LOT],[MMCVLotNo] " +
                                ",[TSC],[LinerPart],[LinerLot],[Quantity],[Unit],[IsPacking],[IsTicket],[IsData],[ExpiryDate],[SupplierResult] " +
                                ",[SampleConfirm],FinalResult,[AdhesiveCode],[AdhesiveLot],[AdhesiveProductDate],[AdhesiveExpiryDate],[AdhesiveReceiveDate],B.Status, A.[RequestNo],[UserUpdate],[EnterTime],[Deviation] " +
                                "FROM [PIMV].[dbo].[IQC_Results] AS A With(NOLOCK) " +
                                "Inner JOIN LAB_Results AS B With(NOLOCK) ON A.RequestNo = B.RequestNo ";
                string condition = string.Empty;

                if (!string.IsNullOrEmpty(lot))
                {
                    condition += $"Where [LOT] = '{lot}' ";
                    if (!string.IsNullOrEmpty(suppliercode) && !string.IsNullOrEmpty(materialcode))
                    {
                        condition += $"And [SupplierCode] = '{suppliercode}' And [MaterialCode] = '{materialcode}' ";
                    }

                    if (d_start is DateTime && d_end is DateTime)
                    {

                        condition += $"And [ReceivedDate] >= '{d_start?.ToString("yyyy-MM-dd")}' And [ReceivedDate] <= '{d_end?.ToString("yyyy-MM-dd")}'";
                    }
                }
                else if (!string.IsNullOrEmpty(suppliercode))
                {
                    condition += $"Where [SupplierCode] = '{suppliercode}' ";

                    if (!string.IsNullOrEmpty(materialcode))
                    {
                        condition += $"And [MaterialCode] = '{materialcode}' ";
                    }

                    if (d_start is DateTime && d_end is DateTime)
                    {

                        condition += $"And [ReceivedDate] >= '{d_start?.ToString("yyyy-MM-dd")}' And [ReceivedDate] <= '{d_end?.ToString("yyyy-MM-dd")}'";
                    }
                }
                else if (d_start is DateTime && d_end is DateTime)
                {

                    condition += $"And [ReceivedDate] >= '{d_start?.ToString("yyyy-MM-dd")}' And [ReceivedDate] <= '{d_end?.ToString("yyyy-MM-dd")}'";
                }

                if (!string.IsNullOrEmpty(condition))
                {
                    query = query + condition;
                    var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query, null);
                    return data;
                }
                else
                {
                    exception = "";
                    return null;
                }                
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return null;
            }
        }

        public static DataTable GET_Results(out string exception, DateTime d_start, DateTime d_end)
        {
            try
            {
                if (d_start > d_end)
                {
                    exception = "Time d_start > d_end.";
                    return null;
                }
                else
                {
                    string query = "SELECT [ID],[ReceivedDate],[Customer],[SupplierCode],[MaterialCode],[InvoiceNo],[Phase],[LOT] " +
                                    ",[TSC],[Quantity],[Unit],[IsPacking],[IsTicket],[IsData],[ExpiryDate],[SupplierResult] " +
                                    ",[SampleConfirm], A.[RequestNo],[Department],[Model],[MPN],[ProdDate],[Shift],[Status]" +
                                    ",[TimeCensorship],[UserCensorship],[Result]" +
                                    "FROM [PIMV].[dbo].[IQC_Results] AS A With(NOLOCK) " +
                                    "Inner JOIN LAB_Results AS B With(NOLOCK) ON A.RequestNo = B.RequestNo " +
                                     $"Where ReceivedDate >= '{d_start.ToString("yyyy-MM-dd")}' And ReceivedDate <= '{d_end.ToString("yyyy-MM-dd")}'";
                    var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
                    return data;
                }
                
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return null;
            }
        }

        ///Lấy danh sách các item
        public static Dictionary<string, int> Dic_ItemsTest(out string exception)
        {
            string query_item = "Select Distinct(ItemName) From IQC_MasterItem";
            var data_iname = ModelsProvider.SqlInstance.ExecuteQuery(out exception,SqlProvider.SqlSV.OFC68_PIMV, query_item);
            var dic_iname = new Dictionary<string, int>();
            int index = 1;
            foreach (DataRow itemrow in data_iname.Rows)
            {
                string iname = itemrow["ItemName"].ToString();
                if (!dic_iname.Keys.Contains(iname))
                {
                    dic_iname.Add(iname, index);
                    index++;
                }
            }
            return dic_iname;
        }

        public static bool TSC_Exist(out string exception,int typeId, string tsc)
        {
            exception = string.Empty;
            if (string.IsNullOrEmpty(tsc))
            {
                return false;
            }
            else
            {
                string query = $"Select Count(*) From IQC_Results A Inner Join IQC_Materials B On A.MaterialCode = B.MaterialCode Where TypeID = {typeId} And TSC = '{tsc}'";
                var res = ModelsProvider.SqlInstance.ExecuteScalar(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
                if (res != null && int.Parse(res.ToString()) > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }            
        }

        public static bool Supplier_Exist(out string exception, string lotno)
        {
            exception = string.Empty;
            if (string.IsNullOrEmpty(lotno))
            {
                return false;
            }
            else
            {
                string query = $"Select Count(*) from IQC_Results Where LOT = '{lotno}';";
                var res = ModelsProvider.SqlInstance.ExecuteScalar(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
                if (res != null && int.Parse(res.ToString()) > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }
        public static bool MMCVLotNo_Exist(out string exception, string mmcvLotNo)
        {
            exception = string.Empty;
            if (string.IsNullOrEmpty(mmcvLotNo))
            {
                return false;
            }
            else
            {
                string query = $"Select count(MMCVLotNo) from [IQC_Results] where MMCVLotNo = '{mmcvLotNo}';";
                var res = ModelsProvider.SqlInstance.ExecuteScalar(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
                if (res != null && int.Parse(res.ToString()) > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }
    }
}
