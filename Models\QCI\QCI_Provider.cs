﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.QCI
{
    public class QCI_Provider
    {
        public DataTable QCI_DataTable(string query, object[] parameter)
        {
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return data;
        }

        public bool IsExistLot(string Lotno, string department)
        {
            string query = "Select Count(*) From [MAP_Inprocess] as a With(NoLock) inner join [LAB_Results] as b with(NoLock) " +
                           $"On a.RequestNo = b.RequestNo Where a.Lotno = '{Lotno}' And b.Department = '{department}'";
            var res = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, query);
            if (res != null)
            {
                return (int)res > 0;
            }
            else
            {
                return false;
            }
        }
        public DataTable QCI_Model()
        {
            string query = "Select Distinct(Model) From LAB_MasterRequest Where[Type] = 'QCI'; ";
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }

        public DataTable DataCheckLotno(string serverMMCV, DateTime date)
        {
            DataTable data = null;
            DataProvider.MMCV_Server? server = null;
            switch (serverMMCV)
            {
                case "SMT-C":
                    server = DataProvider.MMCV_Server.Server69_PIMD;
                    break;
                case "SMT-AB":
                    server = DataProvider.MMCV_Server.Server46_PIMD;
                    break;
                default:
                    break;
            }

            if (server != null)
            {
                string start = date.ToString("yyyy-MM-dd");
                string end = (date.AddDays(1)).ToString("yyyy-MM-dd");
                string query = $"SELECT Distinct([Lotno]) FROM [PIMD].[dbo].[PcsResults] With(NoLock) Where RouteId = 200 And [EnterTime] Between '{start}' And '{end}'";
                data = DataProvider.Instance.ExecuteQuery((DataProvider.MMCV_Server)server, query);
            }

            return data;

        }
    }
}
