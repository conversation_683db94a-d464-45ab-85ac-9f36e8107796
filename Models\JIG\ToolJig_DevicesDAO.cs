﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.JIG
{
    public class ToolJig_DevicesDAO
    {
        public object Get_DeviceSerialID(string toolJigID, string deviceID)
        {
            string query = "Select [SerialID] From [ToolJig_Devices] Where [ToolJigID] = @ToolJigID And [DeviceID] = @DeviceID ";
            var res = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server48_PIMV, query, new object[] { toolJigID, deviceID });
            return res;
        }
        public bool ToolJig_Device_Add(string toolJigID, string deviceID)
        {
            var data = (new JIG_Provider()).ToolJig_Table(toolJigID,false);
            if (data != null && data.Rows.Count > 0)
            {
                //Add table Devices
                string query = "Insert Into [ToolJig_Devices] ([ToolJigID],[DeviceID]) Values ( @ToolJigID , @DeviceID )";
                var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server48_PIMV, query, new object[] { toolJigID, deviceID });
                if (res > 0)
                {
                    //Add table Quanlification
                    TimesPunch item = new TimesPunch(data.Rows[0]);
                    var areaID = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, $"SELECT [AreaID] FROM[PIMV].[dbo].[MMCV_Areas] Where AreaName = '{item.Area}'");
                    var modelID = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, $"SELECT [SerialID] FROM [PIMV].[dbo].[MMCV_Models] Where Model = '{item.Model}'");
                    if (areaID != null) areaID = 0;
                    if (modelID != null) modelID = 0;
                    string query_qualification = "Insert Into MQ_ToolJigQualification ([AreaID],[ProductModelID],[LineID],[ProcessID],[ToolJigID],[MachineID],[ReportLink],[ApprovedDate],[Status],[DateUpdate],[UserUpdate]) " +
                         $"Values ( '{areaID}' , '{modelID}' , '{item.Line}' , '{item.ProcessID}' , '{item.Jig_Name}' , '{deviceID}' , '{item.ReportLink}' , FORMAT(GETDATE(), 'yyyy-MM-dd HH:mm:ss') , 1 , FORMAT(GETDATE(), 'yyyy-MM-dd') , '{item.StaffNo}' );";
                    var result = ModelsProvider.SqlInstance.ExecuteNonQuery(out string exception, SqlProvider.SqlSV.OFC68_PIMV, query_qualification);
                    return true;
                }
                else
                {
                    return false;
                }                  
            }
            else
            {
                return false;
            }
        }

        public bool ToolJig_Device_Edit(int serialID, string toolJigID, string deviceID)
        {
            string query = "Update [ToolJig_Devices] Set [ToolJigID] = @ToolJigID , [DeviceID] = @DeviceID Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server48_PIMV, query, new object[] { toolJigID, deviceID, serialID });
            return res > 0;
        }

        public bool ToolJig_Device_Delete(int serialID)
        {
            try
            {
                string query_sl = "Select Top(1) * From [ToolJig_Devices] Where [SerialID] = " + serialID;
                var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server48_PIMV, query_sl);
                if (data != null && data.Rows.Count == 1)
                {
                    string jigid = data.Rows[0]["ToolJigID"].ToString();
                    string deviceid = data.Rows[0]["DeviceID"].ToString();
                    string query_dl = $"Delete From [MQ_ToolJigQualification] Where ToolJigID = '{jigid}' And MachineID = '{deviceid}';";
                    DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query_dl);
                    string query = "Delete From [ToolJig_Devices] Where [SerialID] = @SerialID ";
                    DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server48_PIMV, query, new object[] { serialID });
                }
                return true;
            }
            catch (Exception)
            {
                return false;
            }
            
        }

    }
}
