﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using AdysTech.CredentialManager;

namespace Models
{
    public class UserProvider
    {
        public static void Reset()
        {
            instance = null;
        }
        private static string functionActive;

        private static UserProvider instance;
        public static UserProvider Instance { get { if (instance == null) instance = new UserProvider(); return instance; } private set => instance = value; }

        private static bool isLogin;
        public static bool IsLogin { get => isLogin; set => isLogin = value; }

        private MMCV_USER dataUser;
        public MMCV_USER DataUser { get { if (dataUser == null) dataUser = new MMCV_USER(); return dataUser; } set => dataUser = value; }

        public static string FunctionActive { get => functionActive; set => functionActive = value; }

        private UserProvider() { }

        public DataTable Table_User(string username)
        {
            string query = "Select * From MMCV_USER Where [Username] = @username ";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { username });
            return data;
        }

       
        public bool LoginUser(string _username, string _password)
        {
            string query = "Select * From MMCV_USER Where Username = @Username And Password = @Password";
            object[] parameter = new object[] { _username, _password };
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            if (data != null && data.Rows.Count > 0)
            {
                DataRow row = data.Rows[0];
                DataUser.SetValue(row);
                IsLogin = true;
            }
            else
            {
                DataUser.ResetValue();
                IsLogin = false;
            }
            return IsLogin;
        }
        public void LogOut()
        {
            DataUser.ResetValue();
            IsLogin = false;
        }

        public bool CheckPassword(string _password)
        {
            string query = "Select * From MMCV_USER Where Username = @Username And Password = @Password ";
            var parameter = new Object[] { UserProvider.Instance.DataUser.Username, _password };
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            if (data != null && data.Rows.Count > 0)
            {
                return true;
            }
            else return false;
        }

        public bool ChangePassword(string _newpassword)
        {
            string querry = "Update [MMCV_USER] Set [Password] = @Password Where [Username] = @Username ";
            var parameter = new object[] { _newpassword, UserProvider.Instance.DataUser.Username };
            var _res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, querry, parameter);
            return _res > 0;
        }

        #region PERMISSION
        public DataTable DataPermission(string _functionID, string _Username = null)
        {
            int i = 1;
            object[] parameter = new object[i];
            string query = "SELECT A.[Username],[FullName],[FunctionID],[IsAccept],[Roles],[EnterTime] FROM MMCV_Permission as a inner Join MMCV_USER as b On a.Username = b.Username Where FunctionID = @FunctionID and b.Status != 0";
            parameter[i - 1] = _functionID;
            if (!string.IsNullOrEmpty(_Username))
            {
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _Username;
                query = query + " And A.Username = @Username ";
            }

            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return data;
        }
        /// <summary>
        /// Check User can use for function add 
        /// </summary>
        /// <param name="_FunctionID"></param>
        /// <param name="_Username"></param>
        /// <returns></returns>
        public bool Exist_Username(string _FunctionID, string _Username)
        {
            string query_user = "SELECT COUNT(Username) FROM [PIMV].[dbo].[MMCV_USER] Where Username = @UserName ";
            var para_user = new object[] { _Username };
            var data = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, query_user, para_user);
            if (data != null && (int)data > 0)
            {
                string query_permission = "SELECT COUNT(Username) FROM [MMCV_Permission] Where FunctionID = @Function And Username = @Username ";
                var para_permission = new object[] { _FunctionID, _Username };
                var res = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, query_permission, para_permission);
                if (res != null && (int)res == 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }



        public List<MMCV_Permission> ListPermission(string _idFunction)
        {
            if (string.IsNullOrEmpty(_idFunction))
            {
                return null;
            }
            string query = "Select * From [MMCV_Permission] Where [FunctionID] =  '" + _idFunction + "'";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);

            if (data != null && data.Rows.Count > 0)
            {
                var myPer = new List<MMCV_Permission>();
                foreach (DataRow item in data.Rows)
                {
                    myPer.Add(new MMCV_Permission(item));
                }
                return myPer;
            }
            else
            {
                return null;
            }
        }

        public bool Add_Permission(MMCV_Permission permission)
        {
            string query = "Insert Into [MMCV_Permission] ([Username],[FunctionID],[IsAccept],[Roles],[EnterTime]) Values ( @Username , @FunctionID , @IsAccept , @Roles , @EnterTime )";
            var parameter = new object[] { permission.Username, permission.FunctionID, permission.IsAccept, permission.Roles, permission.EnterTime };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Edit_Permission(MMCV_Permission permission)
        {
            string query = "Update [MMCV_Permission] Set [IsAccept] = @IsAccept , [Roles] = @Roles ,[EnterTime] = @EnterTime Where [Username] =  @Username And [FunctionID] = @FunctionID ";
            var parameter = new object[] { permission.IsAccept, permission.Roles, permission.EnterTime, permission.Username, permission.FunctionID };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Delete_Permission(MMCV_Permission permission)
        {
            string query = "Delete From [MMCV_Permission] Where [Username] =  @Username And [FunctionID] = @FunctionID ";
            var parameter = new object[] { permission.Username, permission.FunctionID };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        #endregion

        delegate void Credentials(NetworkCredential cred);
        private string username = "mmcv-swuser";
        private string password = "A86js659nk";
        private string domain = "MMCV";
        public void Credential_Add(string path)
        {
            NetworkCredential cred = new NetworkCredential(username, password, domain);
            CredentialManager.SaveCredentials(path, cred, CredentialType.Windows);

        }
        public void Credential_Remove(string path)
        {
            try
            {
                CredentialManager.RemoveCredentials(path, CredentialType.Windows);

            }
            catch (Exception)
            {
            }
        }
    }
}
