﻿using System;
using System.Data;

namespace Models.DAO_LAB
{
    public class LAB_Results
    {
        private long serialID;
        private string requestNo;
        private DateTime? timeRequest;
        private string userRequest;
        private string department;
        private string model;
        private string mPN;
        private string lotno;
        private DateTime? prodDate;
        private string shift;
        private string line;
        private string status;
        private string result;

        public long SerialID { get => serialID; set => serialID = value; }
        public string RequestNo { get => requestNo; set => requestNo = value; }
        public DateTime? TimeRequest { get => timeRequest; set => timeRequest = value; }
        public string UserRequest { get => userRequest; set => userRequest = value; }
        public string Department { get => department; set => department = value; }
        public string Model { get => model; set => model = value; }
        public string MPN { get => mPN; set => mPN = value; }
        public string Lotno { get => lotno; set => lotno = value; }
        public DateTime? ProdDate { get => prodDate; set => prodDate = value; }
        public string Shift { get => shift; set => shift = value; }
        public string Line { get => line; set => line = value; }
        public string Status { get => status; set => status = value; }
        public string Result { get => result; set => result = value; }
        public LAB_Results() { }
        public LAB_Results(DataRow row)
        {
            this.SerialID = (long)row["SerialID"];
            this.RequestNo = row["RequestNo"].ToString();
            this.TimeRequest = (DateTime?)row["TimeRequest"];
            this.UserRequest = row["UserRequest"].ToString();
            this.Department = row["Department"].ToString();
            this.Model = row["Model"].ToString();
            this.MPN = row["MPN"].ToString();
            this.Lotno = row["Lotno"].ToString();
            this.ProdDate = (DateTime?)row["ProdDate"];
            this.Shift = row["Shift"].ToString();
            this.Line = row["Line"].ToString();
            this.Status = row["Status"].ToString();
            this.Result = row["Result"].ToString();
        }
    }
}
