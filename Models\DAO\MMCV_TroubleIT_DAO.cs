﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.DAO
{
    public class MMCV_TroubleIT_DAO
    {
        public DataTable MMCV_TroubleIT_Table()
        {
            string query = "Select TOP (1000) * FROM [PIMV].[dbo].[MMCV_TroubleIT] Where [Deleted] = 0 Order by EnterTime desc";
            DataTable data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }

        public DataTable MMCV_TroubleIT_Table(string query)
        {
            DataTable data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }

        public DataTable MMCV_TroubleIT_Table(long SerialID)
        {
            string query = "Select * From [PIMV].[dbo].[MMCV_TroubleIT] Where [SerialID] = " + SerialID;
            DataTable data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }

        public bool MMCV_TroubleIT_Delete(MMCV_TroubleIT myItem)
        {
            string query = "Update [PIMV].[dbo].[MMCV_TroubleIT] Set [Deleted] = 1, [DeletedBy] = '" + Environment.UserName + "' Where [SerialID] = " + myItem.SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        public bool MMCV_TroubleIT_ExcQuery(string query, object[] parameter)
        {
            int res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

    }
}
