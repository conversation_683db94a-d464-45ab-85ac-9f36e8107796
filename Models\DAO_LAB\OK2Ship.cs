﻿using System;

namespace Models.DAO_LAB
{
    public class OK2Ship
    {
        private long serialID;
        private string model;
        private string mCO;
        private string oDB;
        private string buildConfig;
        private string lotNo;
        private int? qtyDelivery;
        private DateTime? oK2BuilDate;
        private DateTime? oK2ShipDate;
        private string shippingTo;
        private string shippingFrom;
        private int? qtyLot;
        private int? qtyPanel;
        private string sizePanel;
        private string rollPanel;
        private string straightYield;
        private string processYield;
        private string electricalYield;
        private string finalYield;
        private string overallYield;
        private string targetYield;
        private int? weeksID;
        private string staffNo;
        private string pathOK2Ship;
        private DateTime? enterTime;
        private string status;

        public long SerialID { get => serialID; set => serialID = value; }
        public string Model { get => model; set => model = value; }
        public string MCO { get => mCO; set => mCO = value; }
        public string ODB { get => oDB; set => oDB = value; }
        public string BuildConfig { get => buildConfig; set => buildConfig = value; }
        public string LotNo { get => lotNo; set => lotNo = value; }
        public int? QtyDelivery { get => qtyDelivery; set => qtyDelivery = value; }
        public DateTime? OK2BuilDate { get => oK2BuilDate; set => oK2BuilDate = value; }
        public DateTime? OK2ShipDate { get => oK2ShipDate; set => oK2ShipDate = value; }
        public string ShippingTo { get => shippingTo; set => shippingTo = value; }
        public string ShippingFrom { get => shippingFrom; set => shippingFrom = value; }
        public int? QtyLot { get => qtyLot; set => qtyLot = value; }
        public int? QtyPanel { get => qtyPanel; set => qtyPanel = value; }
        public string SizePanel { get => sizePanel; set => sizePanel = value; }
        public string RollPanel { get => rollPanel; set => rollPanel = value; }
        public string StraightYield { get => straightYield; set => straightYield = value; }
        public string ProcessYield { get => processYield; set => processYield = value; }
        public string ElectricalYield { get => electricalYield; set => electricalYield = value; }
        public string FinalYield { get => finalYield; set => finalYield = value; }
        public string OverallYield { get => overallYield; set => overallYield = value; }
        public string TargetYield { get => targetYield; set => targetYield = value; }
        public int? WeeksID { get => weeksID; set => weeksID = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }
        public string Status { get => status; set => status = value; }
        public string PathOK2Ship { get => pathOK2Ship; set => pathOK2Ship = value; }
    }
}
