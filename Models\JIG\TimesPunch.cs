﻿using System;
using System.Data;

namespace Models.JIG
{
    public class TimesPunch
    {
        private int serialID;
        private string area;
        private string line;
        private string model;
        private string processID;
        private string deviceID;
        private string type;
        private DateTime? startedDate;
        private int? numDaysUse;
        private DateTime? expirationDate;
        private int? warningDay;
        private string jig_Name;
        private string jig_Type;
        private string shtBarcode;
        private string partNumber;
        private int? jig_Punch;
        private int? jig_Limit;
        private int? warningPunch;
        private int? numResets;
        private long? totalResets;
        private bool? onlyOnce;
        private int? status;
        private string statusContent;
        private string staffNo;
        private DateTime? lastTime;
        private string reportLink;



        public int SerialID { get => serialID; set => serialID = value; }
        public string StatusContent { get => statusContent; set => statusContent = value; }
        public string Area { get => area; set => area = value; }
        public string Line { get => line; set => line = value; }
        public string Model { get => model; set => model = value; }
        public string ProcessID { get => processID; set => processID = value; }
        public string DeviceID { get => deviceID; set => deviceID = value; }
        public string Type { get => type; set => type = value; }
        public string Jig_Name { get => jig_Name; set => jig_Name = value; }
        public string Jig_Type { get => jig_Type; set => jig_Type = value; }
        public string ShtBarcode { get => shtBarcode; set => shtBarcode = value; }
        public string PartNumber { get => partNumber; set => partNumber = value; }
        public int? Jig_Punch { get => jig_Punch; set => jig_Punch = value; }
        public int? WarningPunch { get => warningPunch; set => warningPunch = value; }
        public int? Jig_Limit { get => jig_Limit; set => jig_Limit = value; }        
        public DateTime? StartedDate { get => startedDate; set => startedDate = value; }
        public int? NumDaysUse { get => numDaysUse; set => numDaysUse = value; }
        public int? WarningDay { get => warningDay; set => warningDay = value; }
        public DateTime? ExpirationDate { get => expirationDate; set => expirationDate = value; }
        public int? NumResets { get => numResets; set => numResets = value; }
        public long? TotalResets { get => totalResets; set => totalResets = value; }
        public bool? OnlyOnce { get => onlyOnce; set => onlyOnce = value; }
        public int? Status { get => status; set => status = value; }
        
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? LastTime { get => lastTime; set => lastTime = value; }
        public string ReportLink { get => reportLink; set => reportLink = value; }

        public TimesPunch() { }
        public TimesPunch(DataRow row)
        {
            //Serial ID
            this.SerialID = (int)row["SerialID"];
            this.Area = row["Area"].ToString();
            this.Line = row["Line"].ToString();
            this.Model = row["Model"].ToString();
            //ProcessID
            this.ProcessID = row["ProcessID"].ToString();
            //DeviceID
            this.DeviceID = row["DeviceID"].ToString();
            //Type
            this.Type = row["Type"].ToString();
            //Jig_Name
            this.Jig_Name = row["Jig_Name"].ToString();
            this.Jig_Type = row["Jig_Type"].ToString();
            //ShtBarcode
            this.ShtBarcode = row["ShtBarcode"].ToString();
            //Part Number
            this.PartNumber = row["PartNumber"].ToString();
            //JIG_punch
            if (row["Jig_Punch"].ToString() == "")
            {
                this.Jig_Punch = null;
            }
            else
            {
                this.Jig_Punch = (int)row["Jig_Punch"];
            }
            //WarningPunch
            if (string.IsNullOrEmpty(row["WarningPunch"].ToString()))
            {
                this.WarningPunch = null;
            }
            else { this.WarningPunch = (int)row["WarningPunch"]; }
            //Jig_Limit
            if (string.IsNullOrEmpty(row["Jig_Limit"].ToString()))
            {
                this.Jig_Limit = null;
            }
            else this.Jig_Limit = (int)row["Jig_Limit"];
            //Number reset
            this.NumResets = (int)row["NumResets"];
            //Number Total reset
            if (string.IsNullOrEmpty(row["TotalResets"].ToString()))
            {
                this.TotalResets = null;
            }
            else { this.TotalResets = (long)row["TotalResets"]; }
            //Start date
            if (string.IsNullOrEmpty(row["StartedDate"].ToString())) this.StartedDate = null;
            else this.StartedDate = (DateTime)row["StartedDate"];
            //Num day use
            if (!string.IsNullOrEmpty(row["NumDaysUse"].ToString())) { this.NumDaysUse = (int)row["NumDaysUse"]; }
            //WarningDay
            if (string.IsNullOrEmpty(row["WarningDay"].ToString()))
            {
                this.WarningDay = null;
            }
            else { this.WarningDay = (int)row["WarningDay"]; }
            //ExpirationDate
            if (this.StartedDate != null && this.NumDaysUse != null)
            {
                this.ExpirationDate = ((DateTime)StartedDate).AddDays((int)NumDaysUse);
            }
            //OnlyOnce
            if (string.IsNullOrEmpty(row["OnlyOnce"].ToString())) this.OnlyOnce = null;
            else this.OnlyOnce = (bool)row["OnlyOnce"];
            //Status
            this.Status = (int)row["Status"];
            switch (this.Status)
            {
                case 0:
                    this.StatusContent = "Need repair";
                    break;
                case 1:
                    this.StatusContent = "OK";
                    break;
                case 2:
                    this.StatusContent = "Warning";
                    break;
                case 3:
                    this.StatusContent = "Requested";
                    break;
                default:
                    break;
            }
            //StaffNo
            this.StaffNo = row["StaffNo"].ToString();
            //Last Time
            if (row["LastTime"].ToString() == "")
            {
                this.LastTime = null;
            }
            else
            {
                this.LastTime = (DateTime)row["LastTime"];
            }
            //Report link
            this.ReportLink = row["ReportLink"].ToString();
        }

        public bool CheckValue()
        {
            if (string.IsNullOrEmpty(Area) || string.IsNullOrEmpty(Line) ||
                string.IsNullOrEmpty(Model) || string.IsNullOrEmpty(ProcessID) || string.IsNullOrEmpty(DeviceID) ||
                string.IsNullOrEmpty(Jig_Name) || string.IsNullOrEmpty(PartNumber) || OnlyOnce == null)
                return false;
            else
            {
                switch (Type)
                {
                    case "Times Punch":
                        if (Jig_Punch == null || Jig_Limit == null || WarningPunch == null) return false;
                        else return true;
                    case "Used Time":
                        if (StartedDate == null || NumDaysUse == null || WarningDay == null) return false;
                        else return true;
                    default:
                        return false;
                }
            }
            
        }
    }
}
