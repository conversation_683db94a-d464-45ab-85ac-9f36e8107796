﻿#pragma checksum "..\..\..\UserControl_LAB\LAB_MenuUC.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2BBB075CF3A00A1AD568883BA9064CE19F10DE131F77CAF6DA265869D3184169"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_LAB;


namespace SystemMMCV.UserControl_LAB {
    
    
    /// <summary>
    /// LAB_MenuUC
    /// </summary>
    public partial class LAB_MenuUC : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Menu_Setting;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Menu_Create;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Menu_Confirm;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Menu_Upload;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Menu_Censorship;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Menu_Result;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem Menu_ok2ship;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox Scv_mesteritem;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_lab/lab_menuuc.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_LAB\LAB_MenuUC.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Menu_Setting = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 2:
            this.Menu_Create = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 3:
            this.Menu_Confirm = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 4:
            this.Menu_Upload = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 5:
            this.Menu_Censorship = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 6:
            this.Menu_Result = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 7:
            this.Menu_ok2ship = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 8:
            this.Scv_mesteritem = ((System.Windows.Controls.GroupBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

