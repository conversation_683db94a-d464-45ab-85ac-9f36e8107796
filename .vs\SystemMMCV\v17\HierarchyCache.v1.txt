﻿++Solution 'SystemMMCV' ‎ (2 of 2 projects)
i:{00000000-0000-0000-0000-000000000000}:SystemMMCV.sln
++Models
i:{00000000-0000-0000-0000-000000000000}:Models
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Properties
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\properties\
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\properties\
++References
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++DAO
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao\
++DAO_LAB
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\
++Employees
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\employees\
++IQC
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\iqc\
++JIG
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\jig\
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\jig\
++JIG_Details
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\jig_details\
++LAB
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\lab\
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\lab\
++Notification
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\notification\
++OBA
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\
++ORT
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ort\
++PIMV
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\
++QCI
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\qci\
++Record
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\record\
++SHIP
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ship\
++AdminProvider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\adminprovider.cs
++DataProvider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dataprovider.cs
++FuncProvider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\funcprovider.cs
++MMCV_Function.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\mmcv_function.cs
++MMCV_Permission.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\mmcv_permission.cs
++MMCV_TroubleIT.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\mmcv_troubleit.cs
++MMCV_USER.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\mmcv_user.cs
++ModelsProvider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\modelsprovider.cs
++packages.config
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\packages.config
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\packages.config
++SERVER.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\server.cs
++UserProvider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\userprovider.cs
++AssemblyInfo.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\properties\assemblyinfo.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\properties\assemblyinfo.cs
++AdysTech.CredentialManager
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
++Microsoft.CSharp
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Core
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Data
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Data.DataSetExtensions
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Net.Http
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Xml
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Xml.Linq
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++VBIDE
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++MMCV_TroubleIT_DAO.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao\mmcv_troubleit_dao.cs
++LAB_MasterRequest.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\lab_masterrequest.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\lab_masterrequest.cs
++LAB_MesterItems.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\lab_mesteritems.cs
++LAB_Provider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\lab_provider.cs
++LAB_Results.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\lab_results.cs
++OK2Ship.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\ok2ship.cs
++OK2Ship_Details.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\ok2ship_details.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\ok2ship_details.cs
++OK2Ship_Maps.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\ok2ship_maps.cs
++OK2Ship_Weeks.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\ok2ship_weeks.cs
++Provider_OK2Ship.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\dao_lab\provider_ok2ship.cs
++EmployeesDAO.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\employees\employeesdao.cs
++IQC_Details.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\iqc\iqc_details.cs
++IQC_Provider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\iqc\iqc_provider.cs
++JIG_Provider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\jig\jig_provider.cs
++TimesPunch.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\jig\timespunch.cs
++ToolJig_DevicesDAO.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\jig\tooljig_devicesdao.cs
++JIG_DetailDAO.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\jig_details\jig_detaildao.cs
++JIG_DetailsDTO.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\jig_details\jig_detailsdto.cs
++LAB_Details.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\lab\lab_details.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\lab_details.cs
++NotificationDAO.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\notification\notificationdao.cs
++AcceptableQualityLevel.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\acceptablequalitylevel.cs
++OBA_CosmeticInspection.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_cosmeticinspection.cs
++OBA_Functions.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_functions.cs
++OBA_LotShipment.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_lotshipment.cs
++OBA_Machine.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_machine.cs
++OBA_MasterItems.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_masteritems.cs
++OBA_MasterRequest.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_masterrequest.cs
++OBA_Packages.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_packages.cs
++OBA_PcsBarcodeGrade.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_pcsbarcodegrade.cs
++OBA_Result.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\oba_result.cs
++ProviderOBA.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\oba\provideroba.cs
++ORT_Detail.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ort\ort_detail.cs
++ORT_Master.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ort\ort_master.cs
++ORT_Result.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ort\ort_result.cs
++ORT_Update.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ort\ort_update.cs
++ProviderORT.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ort\providerort.cs
++IQC_Result.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\iqc_result.cs
++LAB_MasterItems.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\lab_masteritems.cs
++Mail_Settings.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\mail_settings.cs
++MMCV_Areas.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\mmcv_areas.cs
++MMCV_Employees.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\mmcv_employees.cs
++MMCV_Lines.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\mmcv_lines.cs
++MMCV_Models.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\mmcv_models.cs
++MMCV_SamplePcsBarcode.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\mmcv_samplepcsbarcode.cs
++OK2Ship_Master.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\ok2ship_master.cs
++OK2Ship_Results.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\ok2ship_results.cs
++PVS_Censorship.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\pvs_censorship.cs
++ToolJig_Details.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\tooljig_details.cs
++ToolJig_Devices.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\tooljig_devices.cs
++ToolJig_MPN.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\tooljig_mpn.cs
++ToolJig_TimesPunch.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\pimv\tooljig_timespunch.cs
++QCI_Provider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\qci\qci_provider.cs
++BoardManage.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\record\boardmanage.cs
++SHIP_Provider.cs
i:{25b526ab-0f08-431f-ba50-7993a16f4301}:d:\mmcv software\systemmmcv\models\ship\ship_provider.cs
++SystemMMCV
i:{00000000-0000-0000-0000-000000000000}:SystemMMCV
++Connected Services
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\connected services\
++Icon
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\icon\
++IconPu.ico
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\icon\iconpu.ico
++Image
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\image\
++IQC_UseCase.png
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\image\iqc_usecase.png
++Mektec.png
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\image\mektec.png
++Plant.gif
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\image\plant.gif
++Round.gif
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\image\round.gif
++Welcome.gif
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\image\welcome.gif
++JsonData
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\jsondata\
++menu.json
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\jsondata\menu.json
++RootMenu.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\jsondata\rootmenu.cs
++MyControl
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\mycontrol\
++MyDateTimePicker.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\mycontrol\mydatetimepicker.xaml
++MyUserControl
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\myusercontrol\
++NotificationUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\myusercontrol\notificationuc.xaml
++PermisstionItemUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\myusercontrol\permisstionitemuc.xaml
++ShowFileFromFolder.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\myusercontrol\showfilefromfolder.xaml
++Usercontrol_Employees
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_employees\
++UserControl_HRM
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_hrm\
++UserControl_IQC
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\
++UserControl_JIG
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\
++UserControl_LAB
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\
++ConfirmPcsBarcodeORT.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\confirmpcsbarcodeort.xaml
++CreateWeekUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\createweekuc.xaml
++LAB_CensorshipUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_censorshipuc.xaml
++LAB_ConfirmUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_confirmuc.xaml
++LAB_ConfirmUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_confirmuc.xaml.cs
++LAB_CreateRequest.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_createrequest.xaml
++LAB_ItemLinkMachine.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_itemlinkmachine.xaml
++LAB_LinkPcsBarcodeWD.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_linkpcsbarcodewd.xaml
++LAB_MenuUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_menuuc.xaml
++LAB_MesterItemsUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_mesteritemsuc.xaml
++LAB_MesterItemsUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_mesteritemsuc.xaml.cs
++LAB_MesterRequestUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_mesterrequestuc.xaml
++LAB_ReferenceStandards.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_referencestandards.xaml
++LAB_SearchUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_searchuc.xaml
++LAB_UploadMachineLog.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_uploadmachinelog.xaml
++LAB_UploadUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_uploaduc.xaml
++LAB_UploadUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_uploaduc.xaml.cs
++ViewFileUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\viewfileuc.xaml
++UserControl_OBA
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\
++UserControl_OK2SHIP
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\
++OK2SHIP_Confirm.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_confirm.xaml
++OK2SHIP_MasterSample.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_mastersample.xaml
++OK2SHIP_MasterSample.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_mastersample.xaml.cs
++OK2SHIP_Request.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_request.xaml
++OK2SHIP_RequestSample.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_requestsample.xaml
++OK2SHIP_Upload.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_upload.xaml
++OK2SHIP_Warning.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_warning.xaml
++Usercontrol_ORT
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\
++Usercontrol_QCI
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\
++Usercontrol_SecsionIT
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\
++UserControl_SHIP
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\
++View
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\
++ViewModel
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\
++Declare_ToolsVM.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\jig\declare_toolsvm.cs
++LAB_ReferenceStandardsVM.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\jig\lab_referencestandardsvm.cs
++Warehouse_ConfirmVM.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\jig\warehouse_confirmvm.cs
++Warehouse_historyVM.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\jig\warehouse_historyvm.cs
++Warehouse_InputVM.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\jig\warehouse_inputvm.cs
++Warehouse_OutputVM.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\jig\warehouse_outputvm.cs
++LAB_UploadMachineLogVM.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\lab\lab_uploadmachinelogvm.cs
++AS400.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\as400.cs
++ConverterValues.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\convertervalues.cs
++DisplayProvider.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\displayprovider.cs
++Impersonate.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\impersonate.cs
++RelayCommand.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\relaycommand.cs
++ServiceProvider.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\viewmodel\serviceprovider.cs
++App.config
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\app.config
++App.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\app.xaml
++cloud.ico
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\cloud.ico
++license.txt
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\license.txt
++ParentUserControl.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\parentusercontrol.xaml
++Role_Permission.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\role_permission.xaml
++SystemMMCV_TemporaryKey.pfx
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\systemmmcv_temporarykey.pfx
++Windows Logo.png
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\windows logo.png
++app.manifest
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\properties\app.manifest
++Resources.resx
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\properties\resources.resx
++Settings.settings
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\properties\settings.settings
++BouncyCastle.Cryptography
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++DocumentFormat.OpenXml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Enums.NET
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++EPPlus
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++EPPlus.Interfaces
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++EPPlus.System.Drawing
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++ExcelNumberFormat
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++ExtendedNumerics.BigDecimal
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++ICSharpCode.SharpZipLib
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Irony
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++MaterialDesignColors
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++MaterialDesignThemes.Wpf
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++MathNet.Numerics
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.IO.RecyclableMemoryStream
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.Office.Core
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.Office.Interop.Excel
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.VisualBasic
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.WindowsAPICodePack
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.WindowsAPICodePack.ExtendedLinguisticServices
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.WindowsAPICodePack.Sensors
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.WindowsAPICodePack.Shell
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.WindowsAPICodePack.ShellExtensions
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++Microsoft.Xaml.Behaviors
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++NetworkConnection
++Newtonsoft.Json
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++NPOI.Core
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++NPOI.OOXML
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++NPOI.OpenXml4Net
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++NPOI.OpenXmlFormats
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++OxyPlot
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++PresentationCore
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++PresentationFramework
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++ReachFramework
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++SixLabors.Fonts
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++SixLabors.ImageSharp
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Buffers
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.ComponentModel.Annotations
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.ComponentModel.Composition
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.ComponentModel.DataAnnotations
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Configuration
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Drawing
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.IO
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.IO.Packaging
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Memory
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Numerics
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Numerics.Vectors
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Printing
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Runtime
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Runtime.CompilerServices.Unsafe
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Security
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Security.Cryptography.Algorithms
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Security.Cryptography.Encoding
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Security.Cryptography.Pkcs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Security.Cryptography.Primitives
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Security.Cryptography.X509Certificates
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Security.Cryptography.Xml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Text.Encoding.CodePages
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Text.RegularExpressions
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Threading.Tasks
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Threading.Tasks.Extensions
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Web
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Web.Extensions
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Windows.Forms
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++System.Xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++WindowsBase
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++WpfAnimatedGif
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++XLParser
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:
++MyDateTimePicker.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\mycontrol\mydatetimepicker.xaml.cs
++NotificationUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\myusercontrol\notificationuc.xaml.cs
++PermisstionItemUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\myusercontrol\permisstionitemuc.xaml.cs
++ShowFileFromFolder.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\myusercontrol\showfilefromfolder.xaml.cs
++EmployeesSubmit.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_employees\employeessubmit.xaml
++EmployeesUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_employees\employeesuc.xaml
++HRM_MasterMealTime.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_hrm\hrm_mastermealtime.xaml
++IQC.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc.cs
++IQC_Censorship.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_censorship.xaml
++IQC_CorrectUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_correctuc.xaml
++IQC_CreateRequestAS400.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_createrequestas400.xaml
++IQC_Deviation.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_deviation.xaml
++IQC_ImportStockUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_importstockuc.xaml
++IQC_InputResultWD.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_inputresultwd.xaml
++IQC_MasterColumnsUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_mastercolumnsuc.xaml
++IQC_MasterItem_SubmitWD.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_masteritem_submitwd.xaml
++IQC_MasterItemsUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_masteritemsuc.xaml
++IQC_MaterialsUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_materialsuc.xaml
++IQC_ResultTestUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_resulttestuc.xaml
++IQC_Search.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_search.xaml
++IQC_SupplierUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_supplieruc.xaml
++Item_Detail.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\item_detail.xaml
++Add_Device.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\add_device.xaml
++Declare_Tools.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\declare_tools.xaml
++Edit_History.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\edit_history.xaml
++EPaperConfirm.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\epaperconfirm.xaml
++EPaperUpload.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\epaperupload.xaml
++EPaperUploadForm.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\epaperuploadform.xaml
++JIG_Devices.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_devices.xaml
++JIG_EPaperRequest.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_epaperrequest.xaml
++JIG_Management.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_management.xaml
++JIG_MpnUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_mpnuc.xaml
++JIG_Pemission.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_pemission.xaml
++JIG_ResetUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_resetuc.xaml
++SupportBlock.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\supportblock.xaml
++ToolJigUC_Add.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\tooljiguc_add.xaml
++ToolJigUC_Submit.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\tooljiguc_submit.xaml
++UpdateJigByExcell.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\updatejigbyexcell.xaml
++Warehouse_Confirm.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_confirm.xaml
++Warehouse_History.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_history.xaml
++Warehouse_Input.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_input.xaml
++Warehouse_Output.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_output.xaml
++ConfirmPcsBarcodeORT.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\confirmpcsbarcodeort.xaml.cs
++CreateWeekUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\createweekuc.xaml.cs
++LAB_CensorshipUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_censorshipuc.xaml.cs
++LAB_CreateRequest.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_createrequest.xaml.cs
++LAB_ItemLinkMachine.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_itemlinkmachine.xaml.cs
++LAB_LinkPcsBarcodeWD.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_linkpcsbarcodewd.xaml.cs
++LAB_MenuUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_menuuc.xaml.cs
++LAB_MesterRequestUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_mesterrequestuc.xaml.cs
++LAB_ReferenceStandards.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_referencestandards.xaml.cs
++LAB_SearchUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_searchuc.xaml.cs
++LAB_UploadMachineLog.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\lab_uploadmachinelog.xaml.cs
++ViewFileUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_lab\viewfileuc.xaml.cs
++DetailOBA_WD.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\detailoba_wd.xaml
++Item_BarcodeGradeUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_barcodegradeuc.xaml
++Item_CosmeticUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_cosmeticuc.xaml
++Item_Function.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_function.xaml
++Item_PackingIC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_packingic.xaml
++LotOfShipmentUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\lotofshipmentuc.xaml
++OBA_DetailUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_detailuc.xaml
++OBA_FacaUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_facauc.xaml
++OBA_MachineUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_machineuc.xaml
++OBA_MenuUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_menuuc.xaml
++OBA_MesterAQLUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_mesteraqluc.xaml
++OBA_MesterItemUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_mesteritemuc.xaml
++OBA_MesterRequestUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_mesterrequestuc.xaml
++OBA_RequestUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_requestuc.xaml
++OBA_Search.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_search.xaml
++OpenSourceUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\opensourceuc.xaml
++OK2SHIP_Confirm.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_confirm.xaml.cs
++OK2SHIP_Request.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_request.xaml.cs
++OK2SHIP_RequestSample.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_requestsample.xaml.cs
++OK2SHIP_Upload.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_upload.xaml.cs
++OK2SHIP_Warning.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ok2ship\ok2ship_warning.xaml.cs
++EditDetail.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\editdetail.xaml
++EditResulsUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\editresulsuc.xaml
++ORT_MenuUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\ort_menuuc.xaml
++WPF_InputData.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_inputdata.xaml
++WPF_Master.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_master.xaml
++WPF_Search.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_search.xaml
++WPF_UpdateData.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_updatedata.xaml
++QCI_CheckUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_checkuc.xaml
++QCI_MenuUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_menuuc.xaml
++QCI_RequestUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_requestuc.xaml
++QCI_SearchUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_searchuc.xaml
++EditTroubleUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\edittroubleuc.xaml
++InputTroubleUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\inputtroubleuc.xaml
++Job_UC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\job_uc.xaml
++MenuIT.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\menuit.xaml
++TroubleUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\troubleuc.xaml
++SHIP.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\ship.cs
++SHIP_MenuUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\ship_menuuc.xaml
++SHIP_RequestUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\ship_requestuc.xaml
++SHIP_SearchUC.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\ship_searchuc.xaml
++AdministratorMMCV.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\administratormmcv.xaml
++ChangePassword.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\changepassword.xaml
++Login.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\login.xaml
++ProfileMMCV.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\profilemmcv.xaml
++Register.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\register.xaml
++SoftwareMMCV.xaml
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\softwaremmcv.xaml
++App.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\app.xaml.cs
++ParentUserControl.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\parentusercontrol.xaml.cs
++Role_Permission.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\role_permission.xaml.cs
++Resources.Designer.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\properties\resources.designer.cs
++Settings.Designer.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\properties\settings.designer.cs
++EmployeesSubmit.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_employees\employeessubmit.xaml.cs
++EmployeesUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_employees\employeesuc.xaml.cs
++HRM_MasterMealTime.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_hrm\hrm_mastermealtime.xaml.cs
++IQC_Censorship.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_censorship.xaml.cs
++IQC_CorrectUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_correctuc.xaml.cs
++IQC_CreateRequestAS400.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_createrequestas400.xaml.cs
++IQC_Deviation.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_deviation.xaml.cs
++IQC_ImportStockUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_importstockuc.xaml.cs
++IQC_InputResultWD.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_inputresultwd.xaml.cs
++IQC_MasterColumnsUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_mastercolumnsuc.xaml.cs
++IQC_MasterItem_SubmitWD.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_masteritem_submitwd.xaml.cs
++IQC_MasterItemsUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_masteritemsuc.xaml.cs
++IQC_MaterialsUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_materialsuc.xaml.cs
++IQC_ResultTestUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_resulttestuc.xaml.cs
++IQC_Search.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_search.xaml.cs
++IQC_SupplierUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\iqc_supplieruc.xaml.cs
++Item_Detail.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_iqc\item_detail.xaml.cs
++Add_Device.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\add_device.xaml.cs
++Declare_Tools.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\declare_tools.xaml.cs
++Edit_History.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\edit_history.xaml.cs
++EPaperConfirm.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\epaperconfirm.xaml.cs
++EPaperUpload.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\epaperupload.xaml.cs
++EPaperUploadForm.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\epaperuploadform.xaml.cs
++JIG_Devices.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_devices.xaml.cs
++JIG_EPaperRequest.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_epaperrequest.xaml.cs
++JIG_Management.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_management.xaml.cs
++JIG_MpnUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_mpnuc.xaml.cs
++JIG_Pemission.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_pemission.xaml.cs
++JIG_ResetUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\jig_resetuc.xaml.cs
++SupportBlock.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\supportblock.xaml.cs
++ToolJigUC_Add.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\tooljiguc_add.xaml.cs
++ToolJigUC_Submit.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\tooljiguc_submit.xaml.cs
++UpdateJigByExcell.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\updatejigbyexcell.xaml.cs
++Warehouse_Confirm.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_confirm.xaml.cs
++Warehouse_History.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_history.xaml.cs
++Warehouse_Input.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_input.xaml.cs
++Warehouse_Output.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_jig\warehouse_output.xaml.cs
++DetailOBA_WD.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\detailoba_wd.xaml.cs
++Item_BarcodeGradeUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_barcodegradeuc.xaml.cs
++Item_CosmeticUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_cosmeticuc.xaml.cs
++Item_Function.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_function.xaml.cs
++Item_PackingIC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\item_packingic.xaml.cs
++LotOfShipmentUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\lotofshipmentuc.xaml.cs
++OBA_DetailUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_detailuc.xaml.cs
++OBA_FacaUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_facauc.xaml.cs
++OBA_MachineUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_machineuc.xaml.cs
++OBA_MenuUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_menuuc.xaml.cs
++OBA_MesterAQLUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_mesteraqluc.xaml.cs
++OBA_MesterItemUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_mesteritemuc.xaml.cs
++OBA_MesterRequestUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_mesterrequestuc.xaml.cs
++OBA_RequestUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_requestuc.xaml.cs
++OBA_Search.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\oba_search.xaml.cs
++OpenSourceUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_oba\opensourceuc.xaml.cs
++EditDetail.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\editdetail.xaml.cs
++EditResulsUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\editresulsuc.xaml.cs
++ORT_MenuUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\ort_menuuc.xaml.cs
++WPF_InputData.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_inputdata.xaml.cs
++WPF_Master.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_master.xaml.cs
++WPF_Search.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_search.xaml.cs
++WPF_UpdateData.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ort\wpf_updatedata.xaml.cs
++QCI_CheckUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_checkuc.xaml.cs
++QCI_MenuUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_menuuc.xaml.cs
++QCI_RequestUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_requestuc.xaml.cs
++QCI_SearchUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_qci\qci_searchuc.xaml.cs
++EditTroubleUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\edittroubleuc.xaml.cs
++InputTroubleUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\inputtroubleuc.xaml.cs
++Job_UC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\job_uc.xaml.cs
++MenuIT.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\menuit.xaml.cs
++TroubleUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_secsionit\troubleuc.xaml.cs
++SHIP_MenuUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\ship_menuuc.xaml.cs
++SHIP_RequestUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\ship_requestuc.xaml.cs
++SHIP_SearchUC.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\usercontrol_ship\ship_searchuc.xaml.cs
++AdministratorMMCV.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\administratormmcv.xaml.cs
++ChangePassword.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\changepassword.xaml.cs
++Login.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\login.xaml.cs
++ProfileMMCV.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\profilemmcv.xaml.cs
++Register.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\register.xaml.cs
++SoftwareMMCV.xaml.cs
i:{79205629-e0d1-4c58-9bac-4a72a21c89c1}:d:\mmcv software\systemmmcv\systemmmcv\view\softwaremmcv.xaml.cs
