﻿using System;

namespace Models.OBA
{
    public class OBA_MasterItems
    {
        private int serialID;
        private string type;
        private string model;
        private string mPN;
        private string mirrorTray;
        private string customer;
        private string machine;
        private string jig;
        private string staffNo;
        private DateTime? enterTime;

        public int SerialID { get => serialID; set => serialID = value; }
        public string Type { get => type; set => type = value; }
        public string Model { get => model; set => model = value; }
        public string MPN { get => mPN; set => mPN = value; }
        public string MirrorTray { get => mirrorTray; set => mirrorTray = value; }
        public string Customer { get => customer; set => customer = value; }
        public string Machine { get => machine; set => machine = value; }
        public string Jig { get => jig; set => jig = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }
    }
}
