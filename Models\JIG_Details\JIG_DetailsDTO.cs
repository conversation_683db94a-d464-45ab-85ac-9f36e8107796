﻿

using System;

namespace Models.JIG_Details
{
    public class JIG_DetailsDTO
    {
        private int iD;
        private int toolJigID;
        private int timesReset;
        private int? jig_Punch;
        private int? jig_Limit;
        private DateTime? startedDate;
        private int? numDayUse;
        private DateTime? maintenanceDate;
        private string evidence;
        private string result;
        private string staffNo;
        private DateTime? enterTime;

        public int ID { get => iD; set => iD = value; }
        public int ToolJigID { get => toolJigID; set => toolJigID = value; }
        public int TimesReset { get => timesReset; set => timesReset = value; }
        public int? Jig_Punch { get => jig_Punch; set => jig_Punch = value; }
        public int? Jig_Limit { get => jig_Limit; set => jig_Limit = value; }
        public DateTime? StartedDate { get => startedDate; set => startedDate = value; }
        public int? NumDayUse { get => numDayUse; set => numDayUse = value; }
        public DateTime? MaintenanceDate { get => maintenanceDate; set => maintenanceDate = value; }
        public string Evidence { get => evidence; set => evidence = value; }
        public string Result { get => result; set => result = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }        
    }
}
