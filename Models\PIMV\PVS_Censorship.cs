﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    class PVS_Censorship
    {
    }
    public class PVS_Censorship_EXC
    {
        public static DataTable CencorshipIQC(string _Department, string _Mpn = null, string _RequestNo = null, string _LotNo = null, string _UserName = null)
        {
            object[] parameter = new object[1];
            parameter[0] = _Department;
            int i = 1;
            string query = $"Select A.[SerialID],A.[Department],A.[Model],A.[MPN],[SupplierCode],A.[Lotno],B.MMCVLotNo,A.[ProdDate],A.[Shift],A.[Line],A.[Status],A.[Result],A.[RequestNo],[AS400] From LAB_Results A  " +$"Inner Join IQC_Results B On A.RequestNo = B.RequestNo Where [Status] = 'Censorship' And [Department] = '{_Department}' ";
            if (!string.IsNullOrEmpty(_RequestNo))
            {
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _RequestNo;
                query = query + $" And A.[RequestNo] = '{_RequestNo}' ";
            }
            if (!string.IsNullOrEmpty(_Mpn))
            {
                query = query + $" And [MPN] = '{_Mpn}' ";
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _Mpn;
            }

            if (!string.IsNullOrEmpty(_LotNo))
            {
                query = query + $" And [Lotno] = '{_LotNo}' ";
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _LotNo;
            }
            if (!string.IsNullOrEmpty(_UserName))
            {
                query = query + $" And [UserRequest] = '{_UserName}' ";
                i++;
                Array.Resize(ref parameter, i);
                parameter[i - 1] = _UserName;
            }
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return data;
        }
    }
}
