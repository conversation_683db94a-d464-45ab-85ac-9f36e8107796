﻿

namespace Models.JIG_Details
{
    public class JIG_DetailDAO
    {

        public bool JIG_Detail_Edit(JIG_DetailsDTO itemDetial)
        {
            string query;
            object[] para;
            if (string.IsNullOrEmpty(itemDetial.Evidence))
            {
                query = "Update [ToolJig_Details] Set [Jig_Punch] = @Jig_Punch , [Jig_Limit] = @Jig_Limit , [StartedDate] = @StartedDate , [NumDayUse] = @NumDayUse , [MaintenanceDate] = @MaintenanceDate , [Result] = @Result , [StaffNo] = @StaffNo , [EnterTime] = GetDate() Where [ID] = @ID ";
                para = new object[] { itemDetial.Jig_Punch, itemDetial.Jig_Limit, itemDetial.StartedDate, itemDetial.NumDayUse, itemDetial.MaintenanceDate, itemDetial.Result, itemDetial.StaffNo,itemDetial.ID };
            }
            else
            {
                query = "Update [ToolJig_Details] Set [Jig_Punch] = @Jig_Punch , [Jig_Limit] = @Jig_Limit , [StartedDate] = @StartedDate , [NumDayUse] = @NumDayUse , [MaintenanceDate] = @MaintenanceDate , [Evidence] = @Evidence , [Result] = @Result , [StaffNo] = @StaffNo , [EnterTime] = GetDate() Where [ID] = @ID ";
                para = new object[] { itemDetial.Jig_Punch, itemDetial.Jig_Limit, itemDetial.StartedDate, itemDetial.NumDayUse, itemDetial.MaintenanceDate, itemDetial.Evidence, itemDetial.Result, itemDetial.StaffNo,itemDetial.ID };
            }

            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server48_PIMV, query, para);
            if (res > 0)
            {
                return true;
            }
            else return false;
        }

        public bool JIG_Detail_Add(JIG_DetailsDTO item, string jigName)
        {
            int status = 1;
            if (item.Result == "Fail") status = 0;
            string numdayuse = item.NumDayUse == null ? "null" : item.NumDayUse.ToString();
            string query = "Insert Into ToolJig_Details ([ToolJigID],[TimesReset],[Jig_Punch],[Jig_Limit],[StartedDate],[NumDayUse],[MaintenanceDate],[Evidence],[Result],[StaffNo]) " +
            $"Values ( {item.ToolJigID} , {item.TimesReset} , {item.Jig_Punch} , {item.Jig_Limit} , '{item.StartedDate}' , {numdayuse} , '{ item.MaintenanceDate}' , N'{item.Evidence}' , '{item.Result}' , '{ item.StaffNo}' );" +
            $"Update ToolJig_TimesPunch Set [Jig_Punch] = 0 , [StartedDate] = GetDate() , [NumResets] = [NumResets] + 1 , [TotalResets] = [TotalResets] + {item.Jig_Punch} , [Status] = '{status}' Where [SerialID] = {item.ToolJigID} ;";
            
            var res = DataProvider.Instance.ExcuteNonTransaction(DataProvider.MMCV_Server.Server48_PIMV, query);
            if (res > 0)
            {
                ResetRecord(jigName);
                return true;
            }
            else return false;
        }

        void ResetRecord(string barcodeJIG)
        {
            try
            {
                string query = $"Update [BoardManage] Set AfterMaintainUseTimes = 0, [HistoryUseTimes] = 0 Where Barcode = '{barcodeJIG}'";
                DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server46_Record, query);
                DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server69_Record, query);
                //string query_update = $"Update [ToolJig_TimesPunch] Set [Jig_Punch] = 0 Where [Jig_Name] = '{barcodeJIG}';";
                //DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server48_PIMV, query_update);
            }
            catch { }
        }
    }

}
