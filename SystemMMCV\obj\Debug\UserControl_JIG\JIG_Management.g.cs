﻿#pragma checksum "..\..\..\UserControl_JIG\JIG_Management.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "1429146A7070933B6AF0F6B09C9CA59E12B16653FF426166499BB68CD2F8E1C5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_JIG;


namespace SystemMMCV.UserControl_JIG {
    
    
    /// <summary>
    /// JIG_Management
    /// </summary>
    public partial class JIG_Management : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 47 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton rbt;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbb_areas;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbb_lines;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbb_models;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbb_jigtype;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_processid;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_jigid;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_result;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\UserControl_JIG\JIG_Management.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Expander epd_show;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_jig/jig_management.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 46 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.RadioButton)(target)).Checked += new System.Windows.RoutedEventHandler(this.RadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 2:
            this.rbt = ((System.Windows.Controls.RadioButton)(target));
            
            #line 47 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            this.rbt.Checked += new System.Windows.RoutedEventHandler(this.RadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 51 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_AddJIG);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 58 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_AddDevices);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 65 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Export);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 72 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_UploadReport);
            
            #line default
            #line hidden
            return;
            case 7:
            this.cbb_areas = ((System.Windows.Controls.ComboBox)(target));
            
            #line 93 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            this.cbb_areas.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cbb_areas_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.cbb_lines = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.cbb_models = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.cbb_jigtype = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.txt_processid = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.txt_jigid = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            
            #line 127 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_SearchJIG);
            
            #line default
            #line hidden
            return;
            case 14:
            this.dtg_result = ((System.Windows.Controls.DataGrid)(target));
            
            #line 140 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            this.dtg_result.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Event_SelectItem);
            
            #line default
            #line hidden
            
            #line 141 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            this.dtg_result.AutoGeneratedColumns += new System.EventHandler(this.dtg_result_AutoGeneratedColumns);
            
            #line default
            #line hidden
            return;
            case 19:
            this.epd_show = ((System.Windows.Controls.Expander)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 15:
            
            #line 147 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_DeleteJIG);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 148 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Maintanence);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 149 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_EditJIG);
            
            #line default
            #line hidden
            break;
            case 18:
            
            #line 150 "..\..\..\UserControl_JIG\JIG_Management.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_ShowEPaper);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

