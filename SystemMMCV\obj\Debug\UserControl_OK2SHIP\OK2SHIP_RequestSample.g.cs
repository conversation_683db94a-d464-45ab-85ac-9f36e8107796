﻿#pragma checksum "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4CA87F3D1FBF10E53BCBA21CA8D18E2258A690310B3D4DC38F1D4D9F8FD6AEFE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_OK2SHIP;


namespace SystemMMCV.UserControl_OK2SHIP {
    
    
    /// <summary>
    /// OK2SHIP_RequestSample
    /// </summary>
    public partial class OK2SHIP_RequestSample : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 18 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel wpn_ContentHearder;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_model;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_program;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_build;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_config;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker txt_DatePro;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_Shift;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_line;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_lotno;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_quantity;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_data;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_ok2ship/ok2ship_requestsample.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.wpn_ContentHearder = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 2:
            this.txt_model = ((System.Windows.Controls.ComboBox)(target));
            
            #line 20 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
            this.txt_model.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Event_Selected_Model);
            
            #line default
            #line hidden
            return;
            case 3:
            this.txt_program = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txt_build = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.txt_config = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.txt_DatePro = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.txt_Shift = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.txt_line = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.txt_lotno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.txt_quantity = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            
            #line 50 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_SendRequest);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 51 "..\..\..\UserControl_OK2SHIP\OK2SHIP_RequestSample.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Refresh);
            
            #line default
            #line hidden
            return;
            case 13:
            this.dtg_data = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

