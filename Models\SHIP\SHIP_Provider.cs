﻿
using System.Data;

namespace Models.SHIP
{
    public class SHIP_Provider
    {
        public DataTable Shipping_DataTable(string query, object[] parameter)
        {
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return data;
        }

        public DataTable Shipping_Model()
        {
            string query = "Select Distinct(Model) From LAB_MasterRequest Where[Type] = 'Shipping'; ";
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }
    }
}
