﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class Mail_Settings
    {
        public int SerialId { get; set; }
        public string UsedForSW { get; set; }
        public string Classify { get; set; }
        public string ToAddress { get; set; }
        public string CcAddress { get; set; }
        public string BcAddress { get; set; }
        public string Describe { get; set; }

        public Mail_Settings(DataRow row)
        {
            this.SerialId = (int)row["SerialId"];
            this.UsedForSW = row["UsedForSW"].ToString();
            this.Classify = row["Classify"].ToString();
            this.ToAddress = row["ToAddress"].ToString();
            this.CcAddress = row["CcAddress"].ToString();
            this.BcAddress = row["BcAddress"].ToString();
            this.Describe = row["Describe"].ToString();
        }
    }

    public class Mail_Settings_Exc
    {
        public static Mail_Settings Get_InforMail(out string exception, string UsedForSW, string Classify)
        {
            string query = $"Select * From Mail_Settings Where [UsedForSW] = '{UsedForSW}' And [Classify] = '{Classify}';";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            if (data!= null && data.Rows.Count == 1)
            {
                return new Mail_Settings(data.Rows[0]);
            }
            else
            {
                return null;
            }
        }
    }
}
