﻿using System;
using System.Data;

namespace Models.DAO_LAB
{
    public class LAB_MesterItems
    {
        private int serialID;
        private string model;
        private string items;
        private string parentItem;
        private string performed;
        private string listtime;
        private DateTime entertime;

        public int SerialID { get => serialID; set => serialID = value; }
        public string Model { get => model; set => model = value; }
        public string ParentItem { get => parentItem; set => parentItem = value; }
        public string Items { get => items; set => items = value; }
        public string Listtime { get => listtime; set => listtime = value; }
        public DateTime Entertime { get => entertime; set => entertime = value; }
        public string Performed { get => performed; set => performed = value; }
        

        public LAB_MesterItems() { }
        public LAB_MesterItems(DataRow row)
        {
            this.SerialID = (int)row["SerialID"];
            this.Model = row["Model"].ToString();
            this.Items = row["Items"].ToString();
            this.ParentItem = row["ParentItem"].ToString();
            this.Performed = row["Performed"].ToString();
            this.Listtime = row["Listtime"].ToString();
            this.Entertime = (DateTime)row["Entertime"];
        }
    }
}
