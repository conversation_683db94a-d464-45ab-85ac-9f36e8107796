﻿using System;

namespace Models.OBA
{
    public class AcceptableQualityLevel
    {
        private int serialID;
        private string aQL;
        private int quantityMin;
        private int quantityMax;
        private int quantityTaken;
        private int errorLimit;
        private string staffNo;
        private DateTime enterTime;

        public int SerialID { get => serialID; set => serialID = value; }
        public string ALQ { get => aQL; set => aQL = value; }
        public int QuantityMin { get => quantityMin; set => quantityMin = value; }
        public int QuantityMax { get => quantityMax; set => quantityMax = value; }
        public int QuantityTaken { get => quantityTaken; set => quantityTaken = value; }
        public int ErrorLimit { get => errorLimit; set => errorLimit = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime EnterTime { get => enterTime; set => enterTime = value; }
    }
}
