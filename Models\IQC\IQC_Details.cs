﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.IQC
{
    public class IQC_Details
    {

        private string status;
        private int itemIndex;
        private string itemName;
        private string performed;
        private int leadtime;
        private string aQL;
        private string unit;
        private string spec;
        private double? samplingQuantity;
        private int? errorLimit;
        private string note;
        

        public string Status { get => status; set => status = value; }
        public int ItemIndex { get => itemIndex; set => itemIndex = value; }
        public string ItemName { get => itemName; set => itemName = value; }
        public string Performed { get => performed; set => performed = value; }
        public int Leadtime { get => leadtime; set => leadtime = value; }
        public string AQL { get => aQL; set => aQL = value; }
        public string Unit { get => unit; set => unit = value; }
        public string Spec { get => spec; set => spec = value; }
        public double? SamplingQuantity { get => samplingQuantity; set => samplingQuantity = value; }
        public int? ErrorLimit { get => errorLimit; set => errorLimit = value; }
        public string Note { get => note; set => note = value; }
    }
}
