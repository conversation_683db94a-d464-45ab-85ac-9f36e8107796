﻿

namespace Models.OBA
{
    public class OBA_Functions
    {
		private long serialID;// bigint identity(1,1),
		private string shipmentID;// nvarchar(20) not null,
		private string typeCheck;// varchar(50) not null,
		private string machine;// varchar(20) NULL,
		private string jig;// varchar(20) NULL,
		private string calibEffect;// varchar(5) NULL,
		private string maintenanceEffect;// varchar(5) NULL,
		private int sampleQuantity;// int NULL,
		private string pathResult;// nvarchar(500) NULL,
		private string pathEvidence;// nvarchar(500) NULL,
		private string judge;// [varchar] (2) NULL,
        private string visualInspection;
        private int nGQuantity;
        private string errorType;
        public long SerialID { get => serialID; set => serialID = value; }
        public string ShipmentID { get => shipmentID; set => shipmentID = value; }
        public string TypeCheck { get => typeCheck; set => typeCheck = value; }
        public string Machine { get => machine; set => machine = value; }
        public string Jig { get => jig; set => jig = value; }
        public string CalibEffect { get => calibEffect; set => calibEffect = value; }
        public string MaintenanceEffect { get => maintenanceEffect; set => maintenanceEffect = value; }
        public int SampleQuantity { get => sampleQuantity; set => sampleQuantity = value; }
        public string PathResult { get => pathResult; set => pathResult = value; }
        public string PathEvidence { get => pathEvidence; set => pathEvidence = value; }
        public string Judge { get => judge; set => judge = value; }
        public string VisualInspection { get => visualInspection; set => visualInspection = value; }
        public int NGQuantity { get => nGQuantity; set => nGQuantity = value; }
        public string ErrorType { get => errorType; set => errorType = value; }
    }
}
