﻿using System;

namespace Models.OBA
{
    public class OBA_Result
    {
        private long serialID;
        private string shipmentID;
        private string model;
        private string mPN;
        private string stage;
        private DateTime? dateShipping;
        private DateTime? dateOBA;
        private string shifts;
        private int qtyShipping;
        private int qtyCheck;
        private string aQL;
        private string fATP;
        private string staffNo;
        private DateTime? enterTime;
        private string result;

        public long SerialID { get => serialID; set => serialID = value; }
        public string ShipmentID { get => shipmentID; set => shipmentID = value; }
        public string Model { get => model; set => model = value; }
        public string MPN { get => mPN; set => mPN = value; }
        public string Stage { get => stage; set => stage = value; }
        public DateTime? DateShipping { get => dateShipping; set => dateShipping = value; }
        public DateTime? DateOBA { get => dateOBA; set => dateOBA = value; }
        public string Shifts { get => shifts; set => shifts = value; }
        public int QtyShipping { get => qtyShipping; set => qtyShipping = value; }
        public int QtyCheck { get => qtyCheck; set => qtyCheck = value; }
        public string AQL { get => aQL; set => aQL = value; }
        public string FATP { get => fATP; set => fATP = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }
        public string Result { get => result; set => result = value; }

        
    }
}
