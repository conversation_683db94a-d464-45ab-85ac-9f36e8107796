﻿
using System.Data;

namespace Models.OBA
{
    public class ProviderOBA
    {
        //public static void Reset()
        //{
        //    oBA = null;
        //}

        //private static ProviderOBA oBA;
        //public static ProviderOBA OBA { get { if (oBA == null) oBA = new ProviderOBA(); return oBA; } private set => oBA = value; }
        //private ProviderOBA() { }

        #region MasterRequest
        public DataTable GetItemFromModel(string Model)
        {
            string query = "Select * From OBA_Master Where [Model] = @Model Order by [Index]";
            var parameter = new object[] { Model };
            var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res;
        }
        public DataTable GetModelFromMaster()
        {
            string query = "Select Distinct(Model) From OBA_Master";
            var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res;
        }
        public DataTable Table_MasterRequest()
        {
            string query = "Select * From OBA_Master Order By [Model], [Index]";
            var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res;
        }
        public bool MasterRequest_Add(OBA_MasterRequest master)
        {
            var parameter = new object[] { master.Model, master.Index, master.ItemName, master.PathEvidence, master.PathResult, master.StaffNo };
            string query = "Insert Into OBA_Master([Model],[Index],[ItemName],[PathEvidence],[PathResult],[StaffNo]) Values( @Model , @Index , @Item , @Evidence , @PathResult , @StaffNo );";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool MasterRequest_Edit(OBA_MasterRequest master)
        {
            var parameter = new object[] { master.Model, master.Index, master.ItemName, master.PathEvidence, master.PathResult, master.StaffNo, master.EnterTime, master.SerialID };
            string query = "Update OBA_Master Set [Model] = @Model , [Index] = @Index , [ItemName] = @ItemName , [PathEvidence] = @Evidence , [PathResult] = @PathResult , [StaffNo] = @StaffNo , [EnterTime] = @EnterTime Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool MasterRequest__Delete(int SerialID)
        {
            string query = "Delete From [OBA_Master] WHERE [SerialID] = " + SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        #endregion

        #region AQL
        public DataTable TableAQL()
        {
            string query = "Select * From [AcceptableQualityLevel]";
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }
        public DataRow TableAQL(string _AQL, int Quantity)
        {
            var parameter = new object[] { _AQL, Quantity, Quantity };
            string query = "Select * From [AcceptableQualityLevel] Where [AQL] = @AQL And [QuantityMin] < @QuantityMin And [QuantityMax] >= @QuantityMax ";
            var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            if (res != null && res.Rows.Count == 1)
            {
                return res.Rows[0];
            }
            else
            {
                return null;
            }
        }
        public bool AQL_Add(AcceptableQualityLevel qualityLevel)
        {
            var parameter = new object[] { qualityLevel.ALQ, qualityLevel.QuantityMin, qualityLevel.QuantityMax, qualityLevel.QuantityTaken, qualityLevel.ErrorLimit, UserProvider.Instance.DataUser.Username };
            string query = "Insert Into [AcceptableQualityLevel]([AQL] ,[QuantityMin] ,[QuantityMax] ,[QuantityTaken] ,[ErrorLimit] ,[StaffNo]) " +
                "Values ( @AQL , @QuantityMin , @QuantityMax , @QuantityTaken , @ErrorLimit , @StaffNo )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool AQL_Edit(AcceptableQualityLevel qualityLevel)
        {
            var parameter = new object[] { qualityLevel.ALQ, qualityLevel.QuantityMin, qualityLevel.QuantityMax, qualityLevel.QuantityTaken,
                qualityLevel.ErrorLimit, UserProvider.Instance.DataUser.Username,qualityLevel.SerialID };
            string query = "Update [AcceptableQualityLevel] Set [AQL] = @AQL ,[QuantityMin] = @QuantityMin ,[QuantityMax] = @QuantityMax ,[QuantityTaken] = @QuantityTaken ,[ErrorLimit] =  @ErrorLimit ,[StaffNo] = @StaffNo WHERE [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool AQL_Delete(int SerialID)
        {
            string query = "Delete From [AcceptableQualityLevel] WHERE [SerialID] = " + SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        #endregion

        #region OBA_Machine
        public DataTable TableOBA_Machine(string _Machine = null)
        {
            if (string.IsNullOrEmpty(_Machine))
            {
                string query = "Select * From [OBA_Machine] order by SerialID";
                var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
                return res;
            }
            else
            {
                string query = "Select * From [OBA_Machine] Where [Machine] = @Machine ";
                var parameter = new object[] { _Machine };
                var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
                return res;
            }

        }

        public bool Machine_Add(OBA_Machine item)
        {
            var parameter = new object[] { item.Machine, item.Calibration, item.Maintenance, item.StaffNo };

            string query = "Insert Into [OBA_Machine]([Machine],[Calibration],[Maintenance],[StaffNo]) " +
                "Values ( @Machine , @Calibration , @Maintenance , @StaffNo )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Machine_Edit(OBA_Machine item)
        {
            var parameter = new object[] { item.Machine, item.Calibration, item.Maintenance, item.StaffNo, item.EnterTime, item.SerialID };
            string query = "Update [OBA_Machine] Set [Machine] = @Machine ,[Calibration] = @Calibration ,[Maintenance] = @Maintenance ,[StaffNo] = @StaffNo ,[EnterTime] =  @EnterTime WHERE [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool Machine_Delete(int SerialID)
        {
            string query = "Delete From [OBA_Machine] WHERE [SerialID] = " + SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        #endregion

        #region OBA_MasterItems
        public DataTable Get_ListMPN(string Model)
        {
            var parameter = new object[] { Model };
            string query = "Select Distinct(MPN) From [ModelInfo] Where [Model] = @Model ";
            var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server46_PIMD, query, parameter);
            return res;
        }

        public DataTable TableOBA_MasterItems(string _Type, string _Model = null, string _MPN = null)
        {
            object[] parameter;
            string query;
            if (string.IsNullOrEmpty(_Model) || string.IsNullOrEmpty(_MPN))
            {
                parameter = new object[] { _Type };
                query = "Select * From [OBA_MasterItems] Where [Type] = @Type ";
            }
            else
            {
                parameter = new object[] { _Type, _Model, _MPN };
                query = "Select * From [OBA_MasterItems] Where [Type] = @Type And [Model] = @Model And [MPN] = @MPN ";
            }
            var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res;
        }

        public bool MasterItems_Add(OBA_MasterItems item)
        {
            var parameter = new object[] { item.Type, item.Model, item.MPN, item.MirrorTray, item.Customer, item.Machine, item.Jig, item.StaffNo };

            string query = "Insert Into [OBA_MasterItems]([Type],[Model],[MPN],[MirrorTray],[Customer],[Machine],[Jig],[StaffNo]) " +
                "Values ( @Type , @Model , @MPN , @MirrorTray , @Customer , @Machine , @Jig , @StaffNo )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool MasterItems_Edit(OBA_MasterItems item)
        {
            var parameter = new object[] { item.Type, item.Model, item.MPN, item.MirrorTray, item.Customer, item.Machine, item.Jig, item.StaffNo, item.EnterTime, item.SerialID };
            string query = "Update [OBA_MasterItems] Set [Type] = @Type ,[Model] = @Model ,[MPN] = @MPN ,[MirrorTray] = @MirrorTray ,[Customer] =  @Customer , " +
                "[Machine] = @Machine , [Jig] = @Jig , [StaffNo] = @StaffNo , [EnterTime] = @EnterTime WHERE [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool MasterItems_Delete(int SerialID)
        {
            string query = "Delete From [OBA_MasterItems] WHERE [SerialID] = " + SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        #endregion

        #region OBA_Result

        public DataRow DataRow_OBA_Result(string ShipmentID)
        {
            string query = "Select * From OBA_Results Where [ShipmentID] = @ShipmentID ";
            var res =  DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { ShipmentID });
            if (res != null && res.Rows.Count == 1)
            {
                return res.Rows[0];
            }
            else
            {
                return null;
            }
        }
        public DataTable Data_Result(string condition, object[] parameter = null)
        {
            string query = "Select TOP (10000) * From OBA_Results " + condition + " Order by SerialID";
            if (parameter == null)
            {
                DataTable res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
                return res;
            }
            else
            {
                DataTable res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
                return res;
            }


        }

        public bool OBA_Result_SetAttach(string shipmentID, string attach)
        {
            string query = "Update OBA_Results Set [Attach] = @Attach Where [ShipmentID] = @ShippmentID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { attach, shipmentID });
            return res > 0;
        }

        public DataTable TableResults()
        {
            string query = "Select * From OBA_Results where [Result] = 'On-Going' order by SerialID";
            return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
        }
        public bool Results_Add(OBA_Result item)
        {
            string query = "Insert Into OBA_Results ([ShipmentID],[Model],[MPN] ,[Stage],[DateShipping],[DateOBA],[Shifts],[QtyShipping],[FATP],[StaffNo],[EnterTime],[Result]) " +
                "Values ( @ShipmentID , @Model , @MPN , @Stage , @DateShipping , @DateOBA , @Shifts , @QtyShipping , @FATP , @StaffNo , @EnterTime , @Result )";
            var parameter = new object[] { item.ShipmentID, item.Model, item.MPN, item.Stage, item.DateShipping, item.DateOBA, item.Shifts, item.QtyShipping, item.FATP, item.StaffNo, item.EnterTime, item.Result };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool Results_Edit(OBA_Result item)
        {
            string query = "Update OBA_Results Set [ShipmentID] = @ShipmentID , [Model] = @Model ,[MPN] = @MPN ,[Stage] = @Stage , [DateShipping] = @DateShipping , " +
                "[DateOBA] = @DateOBA ,[Shifts] = @Shifts , [QtyShipping] = @QtyShipping ,[FATP] = @FATP ,[StaffNo] = @StaftNo ,[EnterTime] = @EnterTime ,[Result] = @Result " +
                "Where SerialID = @SerialID ";
            var parameter = new object[] { item.ShipmentID, item.Model, item.MPN, item.Stage, item.DateShipping, item.DateOBA, item.Shifts, item.QtyShipping, item.FATP, item.StaffNo, item.EnterTime, item.Result, item.SerialID };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }



        #endregion

        #region OBA_LotShipment

        public DataTable TableForLotNo(string _LotNo)
        {
            string query = "Select * From OBA_LotShipment Where [Lotno] = @LotNo ";
            var parameter = new object[] { _LotNo };
            var res = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res;
        }
        public DataTable Table_LotShipment(string _ShipmentID, bool? IsOBA = null)
        {
            string query = "Select * From OBA_LotShipment Where [ShipmentID] = @ShipmentID ";
            if (IsOBA == null)
            {
                query = query + "Order by SerialID";
                return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { _ShipmentID });
            }
            else
            {
                query = query + "AND [IsLotOBA] = @IsLotOBA Order by SerialID";
                return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { _ShipmentID, IsOBA });
            }
        }

        public bool LotShipment_Add(OBA_LotShipment item)
        {
            var parameter = new object[] { item.ShipmentID, item.Lotno, item.IsLotOBA, item.Invoice, item.StaffNo };
            string query = "Insert Into OBA_LotShipment ([ShipmentID],[Lotno],[IsLotOBA],[Invoice],[StaffNo]) " +
                "Values ( @ShipmentID , @Lotno , @IsLotOBA , @Invoice , @StaffNo )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool LotShipment_Edit(OBA_LotShipment item)
        {
            var parameter = new object[] { item.Lotno, item.IsLotOBA, item.Invoice, item.StaffNo, item.EnterTime, item.SerialID };
            string query = "Update OBA_LotShipment Set [Lotno] = @Lotno , [IsLotOBA] = @IsLotOBA , [Invoice] = @Invoice ,[StaffNo] = @StaffNo , [EnterTime] = @EnterTime WHERE [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        public bool LotShipment_Delete(int SerialID)
        {
            string query = "Delete From OBA_LotShipment WHERE [SerialID] = " + SerialID;
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        #endregion

        #region Other
        public bool Exe_InputTransaction(string query)
        {
            var res = DataProvider.Instance.ExcuteNonTransaction(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        public DataTable Data_Cosmetic(string _ShipmentID)
        {
            var paramester = new object[] { _ShipmentID };
            string query = "Select * From OBA_CosmeticInspection Where ShipmentID = @ShipmentID ";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, paramester);
            return data;
        }

        public DataTable Data_PcsBarcodeGrade(string _ShipmentID)
        {
            var paramester = new object[] { _ShipmentID };
            string query = "SELECT * FROM OBA_PcsBarcodeGrade Where ShipmentID = @ShipmentID ";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, paramester);
            return data;
        }

        public DataTable Data_Functions(string _ShipmentID)
        {
            var paramester = new object[] { _ShipmentID };
            string query = "SELECT * FROM OBA_Functions Where ShipmentID = @ShipmentID ";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, paramester);
            return data;
        }

        public DataTable Data_Packages(string _ShipmentID)
        {
            var paramester = new object[] { _ShipmentID };
            string query = "SELECT * FROM OBA_Packages Where ShipmentID = @ShipmentID ";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, paramester);
            return data;
        }
        #endregion
    }
}
