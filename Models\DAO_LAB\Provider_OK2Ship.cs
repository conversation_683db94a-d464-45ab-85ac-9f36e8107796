﻿using System;
using System.Data;

namespace Models.DAO_LAB
{
    public class Provider_OK2Ship
    {
        #region MyRegion
        public DataTable DataTable_OK2ShipLink(string _Department)
        {
            string query = "SELECT [Model],[LotNo],[OK2BuilDate],[OK2ShipDate],[ShippingTo],[ShippingFrom],[QtyLot],"  +
                "[OK2ShipID],[Department],[RequestNo] " +
                "FROM [OK2Ship] with(Nolock) Inner Join [OK2Ship_Details] " +
                "ON [OK2Ship].SerialID = [OK2Ship_Details].OK2ShipID " +
                "Where [OK2Ship].[Status] = 'On-Going' And  [OK2Ship_Details].[Status] = 0 And Department = @CQE ";
            object[] parameter = new object[] { _Department };
            DataTable table = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return table;
        }
        #endregion

        #region OK2Ship
        public DataTable Table_OK2Ship(string status)
        {
            string query = "Select * From [OK2Ship] With (Nolock) Where [Status] = @Status ";
            object[] parameter = new object[] { status };
            DataTable table = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return table;
        }
        public DataTable Model_All()
        {
            string query = "SELECT Distinct([Model]) FROM [PIMV].[dbo].[LAB_MasterItems] With (Nolock)";
            DataTable table = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return table;
        }

        public bool OK2Ship_AddItem(OK2Ship oK2Ship)
        {
            string query = "Insert Into OK2Ship ([Model],[MCO],[ODB],[BuildConfig],[LotNo],[QtyDelivery],[OK2BuilDate],[OK2ShipDate]" +
                              ",[ShippingTo],[ShippingFrom],[QtyLot],[QtyPanel],[SizePanel]" +
                              ",[RollPanel],[StraightYield],[ProcessYield],[ElectricalYield]" +
                              ",[FinalYield],[OverallYield],[TargetYield],[StaffNo],[Status]) " +
                           "Values( @Model , @MCO , @ODB , @BuildConfig , @LotNo , @QtyDelivery , @OK2BuilDate , @OK2ShipDate " +
                              ", @ShippingTo , @ShippingFrom , @QtyLot , @QtyPanel , @SizePanel " +
                              ", @RollPanel , @StraightYield , @ProcessYield , @ElectricalYield " +
                              ", @FinalYield , @OverallYield , @TargetYield , @StaffNo , @Status )";
            var parameter = new object[] { oK2Ship.Model, oK2Ship.MCO, oK2Ship.ODB, oK2Ship.BuildConfig, oK2Ship.LotNo
                                          , oK2Ship.QtyDelivery, oK2Ship.OK2BuilDate,oK2Ship.OK2ShipDate
                                          ,oK2Ship.ShippingTo,oK2Ship.ShippingFrom,oK2Ship.QtyLot,oK2Ship.QtyPanel,oK2Ship.SizePanel
                                          , oK2Ship.RollPanel,oK2Ship.StraightYield,oK2Ship.ProcessYield,oK2Ship.ElectricalYield
                                          ,oK2Ship.FinalYield,oK2Ship.OverallYield,oK2Ship.TargetYield,oK2Ship.StaffNo,oK2Ship.Status};
            var res = DataProvider.Instance.ExecuteIdentity(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            if (res != null)
            {
                long serialID = long.Parse(res.ToString());
                string query_detail = "Insert Into OK2Ship_Details([OK2ShipID],[Department],[Status]) Values (" + serialID + ",'IPQC(BE)',0);" +
                                        "Insert Into OK2Ship_Details([OK2ShipID],[Department],[Status]) Values (" + serialID + ",'OQC(BE)',0);" +
                                        "Insert Into OK2Ship_Details([OK2ShipID],[Department],[Status]) Values (" + serialID + ",'CQE',0);";
                var res_detail = DataProvider.Instance.ExcuteNonTransaction(DataProvider.MMCV_Server.Server68_PIMV, query_detail);
                if (res_detail > 0)
                {
                    return true;
                }
                else
                {
                    try
                    {
                        string query_del = "Delete From OK2Ship Where [SerialID] = " + serialID;
                        DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query_del);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message);
                    }
                    
                    return false;
                }
                
            }
            else
            {
                return false;
            }
      
        }

        public bool Edit_OK2Ship(OK2Ship oK2Ship)
        {
            string query = "Update OK2Ship Set [Model] = @Model ,[MCO] = @MCO ,[ODB] = @ODB ,[BuildConfig] = @BuildConfig ,[LotNo] = @Lotno ,[QtyDelivery] = @QtyDelivery ,[OK2BuilDate] = @OK2BuilDate ,[OK2ShipDate] = @OK2ShipDate " +
                              ",[ShippingTo] = @ShippingTo ,[ShippingFrom] = @ShippingFrom ,[QtyLot] = @QtyLot ,[QtyPanel] = @QtyPanel ,[SizePanel] = @SizePanel " +
                              ",[RollPanel] = @RollPanel ,[StraightYield] = @StraightYield ,[ProcessYield] = @ProcessYield ,[ElectricalYield] = @ElectricalYield " +
                              ",[FinalYield] = @FinalYield ,[OverallYield] = @OverallYield ,[TargetYield] = @TargetYield ,[StaffNo] = @StaffNo ,[Status] = @Status Where [SerialID] = @SerialID ";
            var parameter = new object[] { oK2Ship.Model, oK2Ship.MCO, oK2Ship.ODB, oK2Ship.BuildConfig, oK2Ship.LotNo
                                          , oK2Ship.QtyDelivery, oK2Ship.OK2BuilDate,oK2Ship.OK2ShipDate
                                          ,oK2Ship.ShippingTo,oK2Ship.ShippingFrom,oK2Ship.QtyLot,oK2Ship.QtyPanel,oK2Ship.SizePanel
                                          , oK2Ship.RollPanel,oK2Ship.StraightYield,oK2Ship.ProcessYield,oK2Ship.ElectricalYield
                                          ,oK2Ship.FinalYield,oK2Ship.OverallYield,oK2Ship.TargetYield,oK2Ship.StaffNo,oK2Ship.Status, oK2Ship.SerialID};
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Update_PathOK2Ship(string _Path, long _OK2ShipID)
        {
            string query = "Update OK2Ship Set [PathOK2Ship] = @Path Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { _Path , _OK2ShipID });
            return res > 0;
        }

        public bool OK2Ship_UpdateStatus(string _Status, long _OK2ShipID)
        {
            string query = "Update OK2Ship Set [Status] = @Status Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { _Status, _OK2ShipID });
            return res > 0;
        }

        public bool OK2Ship_Delete(long _OK2ShipID)
        {
            string query = "Delete From OK2Ship Where [SerialID] = " + _OK2ShipID + ";" +
                "Delete From OK2Ship_Maps Where [OK2ShipID] = " + _OK2ShipID + ";";
            var res = DataProvider.Instance.ExcuteNonTransaction(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        #endregion

        #region OK2Ship_Weeks
        public DataTable Data_OK2Ship_Weeks(int? _Year = null, string _WeekName = null)
        {
            string query = "Select Top(1000)* From OK2Ship_Weeks With(NoLock) ";
            if (_Year == null && string.IsNullOrEmpty(_WeekName))
            {
                return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            }
            else
            {
                string condition = "";
                int i = 1;
                object[] parameter = new object[i];


                if (_Year != null)
                {
                    condition = condition + "[Year] = @Year ";
                    if (i != 1) Array.Resize(ref parameter, i);
                    parameter[i - 1] = _Year;
                    i++;
                }

                if (!string.IsNullOrEmpty(_WeekName))
                {
                    condition = condition + "[WeekName] = @WeekName ";
                    if (i != 1) Array.Resize(ref parameter, i);
                    parameter[i - 1] = _WeekName;
                    i++;
                }
                query = query + "Where " + condition;
                return DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            }
        }

        public bool Add_OK2Ship_Weeks(OK2Ship_Weeks item)
        {
            var parameter = new object[] { item.Year, item.WeekName, item.DateStart, item.DateEnd, item.RequestNo };
            string query = "Insert Into OK2Ship_Weeks([Year],[WeekName],[DateStart],[DateEnd],[RequestNo]) " +
                            "Values( @Year , @WeekName , @DateStart , @DateEnd , @RequestNo )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Edit_OK2Ship_Weeks(OK2Ship_Weeks item)
        {
            var parameter = new object[] { item.Year, item.WeekName, item.DateStart, item.DateEnd, item.RequestNo, item.SerialID };
            string query = "Update OK2Ship_Weeks Set [Year] = @Year , [WeekName] = @WeekName , [DateStart] = @DateStart , [DateEnd] = @DateEnd , [RequestNo] = @RequestNo Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        #endregion

        #region OK2Ship_Master
        public DataTable DataTable_OK2Ship_Master(string _Model = null)
        {
            if (string.IsNullOrEmpty(_Model))
            {
                return null;
            }
            else
            {
                var _Parameter = new object[] { _Model };
                string query = "Select * From OK2Ship_Master With(Nolock) Where [Model] = @Model Order by [ItemIndex]";
                var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, _Parameter);
                return data;
            }
        }

        public bool AddItem_OK2Ship_Master(string _Model, string _ItemName, int _ItemIndex)
        {
            var _Parameter = new object[] { _Model, _ItemName,_ItemIndex, UserProvider.Instance.DataUser.Username};
            var _Query = "Insert Into OK2Ship_Master([Model],[ItemName],[ItemIndex],[StaffNo]) Values ( @Model , @ItemName , @ItemIndex , @StaffNo )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, _Query, _Parameter);
            return res > 0;
        }
        public bool EditItem_OK2Ship_Master(int _SerialID, string _Model, string _ItemName, int _ItemIndex)
        {
            var _Parameter = new object[] { _Model, _ItemName, _ItemIndex, UserProvider.Instance.DataUser.Username,DateTime.Now, _SerialID };
            var _Query = "Update OK2Ship_Master Set [Model] = @Model ,[ItemName] = @ItemName ,[ItemIndex] = @ItemIndex , [StaffNo] = @StaffNo , [EnterTime] = @EnterTime Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, _Query, _Parameter);
            return res > 0;
        }
        public bool DeleteItem_OK2Ship_Master(int _SerialID)
        {
            var _Parameter = new object[] { _SerialID };
            var _Query = "Delete From OK2Ship_Master Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, _Query, _Parameter);
            return res > 0;
        }
        #endregion

        #region OK2Ship_Maps

        //public GetItemBySerialID
        public DataTable DataTable_Maps(long OK2Ship_ID) 
        {
            string query = "Select * From OK2Ship_Maps With(Nolock) Where [OK2ShipID] = @OK2ShipID ";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, new object[] { OK2Ship_ID });
            return data;
        }

        public bool Edit_OK2ShipMaps(OK2Ship_Maps maps)
        {
            string query = "";
            maps.EnterTime = DateTime.Now;
            if (string.IsNullOrEmpty(maps.Document)) maps.Document = "";
            if (maps.SerialID == null)
            {
                query = "Insert Into OK2Ship_Maps([OK2ShipID],[ItemName],[ItemIndex],[DisplayName],[Document])" +
                    "Values ( @OK2ShipID , @ItemName , @ItemIndex , @DisplayName , @Document )";
            }
            else
            {
                query = "Update OK2Ship_Maps Set [OK2ShipID] = @OK2ShipID ,[ItemName] = @ItemName ,[ItemIndex] = @ItemIndex ,"+
                    "[DisplayName] = @DisplayName ,[Document] = @Document ,[EnterTime] = @EnterTime Where [SerialID] = @SerialID ";
            }
            var parameter = new object[] { maps.OK2ShipID, maps.ItemName, maps.ItemIndex, maps.DisplayName, maps.Document, maps.EnterTime, maps.SerialID };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
        #endregion
    }
}
