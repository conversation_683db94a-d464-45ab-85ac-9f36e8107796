﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.ORT
{
    public class ORT_Result
    {
        private long serialID;
        private string model;
        private string mPN;
        private string itemName;
        private string lotno;
        private DateTime prodate;
        //private DateTime testdate;
        private string line;
        private string shiftWork;
        private string frequency;
        private string spec;
        private int quantity;
        private string result;
        private string note;
        private string stages;
        private string staffNo;
        private DateTime enterTime;

        public long SerialID { get => serialID; set => serialID = value; }
        public string Model { get => model; set => model = value; }
        public string MPN { get => mPN; set => mPN = value; }
        public string ItemName { get => itemName; set => itemName = value; }
        public string Lotno { get => lotno; set => lotno = value; }
        public DateTime Prodate { get => prodate; set => prodate = value; }
        public string Line { get => line; set => line = value; }
        public string ShiftWork { get => shiftWork; set => shiftWork = value; }
        public string Frequency { get => frequency; set => frequency = value; }
        public string Spec { get => spec; set => spec = value; }
        public int Quantity { get => quantity; set => quantity = value; }
        public string Result { get => result; set => result = value; }
        public string Note { get => note; set => note = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime EnterTime { get => enterTime; set => enterTime = value; }
        //public DateTime Testdate { get => testdate; set => testdate = value; }
        public string Stages { get => stages; set => stages = value; }

        public ORT_Result() { }
        public ORT_Result(DataRow row)
        {
            this.SerialID = (long)row["SerialID"];
            this.Model = row["Model"].ToString();
            this.MPN = row["MPN"].ToString();
            this.ItemName = row["ItemName"].ToString();
            this.Lotno = row["Lotno"].ToString();
            this.Prodate = (DateTime)row["Prodate"];
            //this.Testdate = (DateTime)row["Testdate"];
            this.Line = row["Line"].ToString();
            this.ShiftWork = row["ShiftWork"].ToString();
            this.Frequency = row["Frequency"].ToString();
            this.Spec = row["Spec"].ToString();
            this.Quantity = (int)row["Quantity"];
            this.Result = row["Result"].ToString();
            this.Note = row["Note"].ToString();
            this.Stages = row["Stages"].ToString();
            this.StaffNo = row["StaffNo"].ToString();
            this.EnterTime = (DateTime)row["EnterTime"];
        }
    }
}
