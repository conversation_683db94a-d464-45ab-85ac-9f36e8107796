﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models
{
    public class ModelsProvider
    {
        private static SqlProvider sqlInstance;

        private static NpgsqlProvider npgsqlInstance;
        public static SqlProvider SqlInstance { get { if (sqlInstance == null) sqlInstance = new SqlProvider(); return sqlInstance; } private set => sqlInstance = value; }
        internal static NpgsqlProvider NpgsqlInstance { get { if (npgsqlInstance == null) npgsqlInstance = new NpgsqlProvider(); return npgsqlInstance; } private set => npgsqlInstance = value; }
    }

    public class SqlProvider
    {
        public string ConnectString(SqlSV server)
        {
            string TextConnect = null;
            switch (server)
            {
                case SqlSV.OFC68_PIMV:
                    TextConnect = @"Server = ********** ; database = PIMV; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case SqlSV.OFC45_PIMD:
                    TextConnect = @"Server = ********** ; database = PIMD; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case SqlSV.OFC46_PIMD:
                    TextConnect = @"Server = ********** ; database = PIMD; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case SqlSV.OFC48_PIMV:
                    TextConnect = @"Server = ********** ; database = PIMV; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case SqlSV.OFC69_PIMD:
                    TextConnect = @"Server = ********** ; database = PIMD; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case SqlSV.OFC46_Record:
                    TextConnect = @"Server = ********** ; database = Record; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case SqlSV.OFC69_Record:
                    TextConnect = @"Server = ********** ; database = Record; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                default:
                    break;
            }
            return TextConnect;
        }
        public enum SqlSV
        {
            OFC68_PIMV,
            OFC45_PIMD,
            OFC46_PIMD,
            OFC48_PIMV,
            OFC69_PIMD,
            OFC46_Record,
            OFC69_Record,
            NULL
        }

        #region SQLRegion
        /// <summary>
        /// Try vấn dữ liệu
        /// </summary>
        /// <param name="sever"></param>
        /// <param name="query"></param>
        /// <param name="object parameter"></param>
        /// <returns>Thành công trả về khác null</returns>
        public DataTable ExecuteQuery(out string exception, SqlSV sever, string query, object[] parameter = null)
        {
            exception = null;
            DataTable data = new DataTable();
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                command.Parameters.AddWithValue(item, parameter[i]);
                                i++;
                            }
                        }
                    }
                    SqlDataAdapter adapter = new SqlDataAdapter(command);
                    adapter.Fill(data);
                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
                return null;
            }
            return data;
        }

        public int ExecuteNonQuery(out string exception, SqlSV sever, string query, object[] parameter = null)
        {
            exception = null;
            int data = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                if(parameter[i] == null) command.Parameters.AddWithValue(item,DBNull.Value);
                                else command.Parameters.AddWithValue(item, parameter[i]);
                                i++;
                            }
                        }
                    }
                    data = command.ExecuteNonQuery();
                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            return data;
        }

        public object ExecuteScalar(out string exception, SqlSV sever, string query, object[] parameter = null)
        {
            exception = null;
            object data = null ;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                command.Parameters.AddWithValue(item, parameter[i]);
                                i++;
                            }
                        }
                    }
                    data = command.ExecuteScalar();
                    connection.Close();
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            return data;
        }


        public int ExcuteNonTransaction(out string exception, SqlSV sever, string query)
        {
            exception = null;
            int data = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    SqlTransaction transaction = connection.BeginTransaction();
                    try
                    {
                        command.Transaction = transaction;
                        data = command.ExecuteNonQuery();
                        transaction.Commit();
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                    }
                    finally
                    {
                        transaction.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            return data;
        }
        #endregion
    }

    internal class NpgsqlProvider
    {
        public enum NpgsqlSV
        {
            SV81_FoxServer,
            NULL
        }
    }
}
