﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.ORT
{
    public class ProviderORT
    {
        public DataTable ORT_Master_Table()
        {
            string query = "Select * From [PIMV].[dbo].[ORT_Master] order by [ItemIndex],[StageIndex]";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }
    }
}
