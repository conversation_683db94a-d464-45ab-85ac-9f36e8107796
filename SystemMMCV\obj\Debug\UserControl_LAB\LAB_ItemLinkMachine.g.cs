﻿#pragma checksum "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "E15235DC32D9A43BD3647FEE55E0F7BFBACC7A17BB65E18F950D9BB5BAB130B7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_LAB;


namespace SystemMMCV.UserControl_LAB {
    
    
    /// <summary>
    /// LAB_ItemLinkMachine
    /// </summary>
    public partial class LAB_ItemLinkMachine : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 20 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DrawerHost drh_body;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_itemid;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_itemname;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ict_body;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_items;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_itemlinkmachine;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_lab/lab_itemlinkmachine.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 17 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Btn_Refresh);
            
            #line default
            #line hidden
            return;
            case 2:
            this.drh_body = ((MaterialDesignThemes.Wpf.DrawerHost)(target));
            return;
            case 3:
            this.txt_itemid = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txt_itemname = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.ict_body = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 6:
            
            #line 54 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Btn_Submit_Machine);
            
            #line default
            #line hidden
            return;
            case 7:
            this.dtg_items = ((System.Windows.Controls.DataGrid)(target));
            
            #line 65 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
            this.dtg_items.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.dtg_items_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.dtg_itemlinkmachine = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 8:
            
            #line 70 "..\..\..\UserControl_LAB\LAB_ItemLinkMachine.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Btn_Edit_Item);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

