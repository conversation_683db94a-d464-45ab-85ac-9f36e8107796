﻿
using System.Collections.Generic;
using System.Data;

namespace Models
{
    public class AdminProvider
    {
        public static void Reset()
        {
            Admin = null;
        }

        private static AdminProvider admin;

        public static AdminProvider Admin { get { if (admin == null) admin = new AdminProvider(); return admin; } set => admin = value; }

        #region Administrator
        public bool Add_User(string _username, bool _isAdmin,string _fullname,bool _male,string _department)
        {
            string query = "Insert Into [MMCV_USER] (Username,Password,IsAdmin,FullName,Male,Department) Values ( @Username , @Password , @IsAdmin , @FullName , @Male , @Department )";
            var parameter = new object[] { _username, "mmcv1234", _isAdmin, _fullname, _male, _department };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Edit_User(string _username, bool _isAdmin, string _fullname, bool _male, string _department)
        {
            string query = "Update [MMCV_USER] Set IsAdmin = @IsAdmin , FullName = @FullName , [Group] = @Department Where Username = @Username ";
            var parameter = new object[] { _isAdmin, _fullname, _department, _username };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Delete_User(string _username)
        {
            string query = "Delete From [MMCV_USER] Where Username = @Username ";
            var parameter = new object[] { _username };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public List<MMCV_USER> GetDataUser()
        {
            string query = "Select * From [MMCV_USER]";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            if (data != null && data.Rows.Count > 0)
            {
                List<MMCV_USER> myList = new List<MMCV_USER>();
                foreach (DataRow item in data.Rows)
                {
                    myList.Add(new MMCV_USER(item));
                }
                return myList;
            }
            else
            {
                return null;
            }
        }

        #endregion

    }
}
