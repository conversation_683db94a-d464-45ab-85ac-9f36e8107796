﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{25B526AB-0F08-431F-BA50-7993A16F4301}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Models</RootNamespace>
    <AssemblyName>Models</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AdysTech.CredentialManager, Version=2.6.0.0, Culture=neutral, PublicKeyToken=42b78d2d57493ebe, processorArchitecture=MSIL">
      <HintPath>..\packages\AdysTech.CredentialManager.2.6.0\lib\net45\AdysTech.CredentialManager.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdminProvider.cs" />
    <Compile Include="DAO\MMCV_TroubleIT_DAO.cs" />
    <Compile Include="DAO_LAB\LAB_MasterRequest.cs" />
    <Compile Include="DAO_LAB\LAB_MesterItems.cs" />
    <Compile Include="DAO_LAB\LAB_Provider.cs" />
    <Compile Include="DAO_LAB\LAB_Results.cs" />
    <Compile Include="DAO_LAB\OK2Ship.cs" />
    <Compile Include="DAO_LAB\OK2Ship_Details.cs" />
    <Compile Include="DAO_LAB\OK2Ship_Maps.cs" />
    <Compile Include="DAO_LAB\OK2Ship_Weeks.cs" />
    <Compile Include="DAO_LAB\Provider_OK2Ship.cs" />
    <Compile Include="DataProvider.cs" />
    <Compile Include="Employees\EmployeesDAO.cs" />
    <Compile Include="FuncProvider.cs" />
    <Compile Include="IQC\IQC_Details.cs" />
    <Compile Include="IQC\IQC_Provider.cs" />
    <Compile Include="JIG\JIG_Provider.cs" />
    <Compile Include="JIG\TimesPunch.cs" />
    <Compile Include="JIG\ToolJig_DevicesDAO.cs" />
    <Compile Include="JIG_Details\JIG_DetailDAO.cs" />
    <Compile Include="JIG_Details\JIG_DetailsDTO.cs" />
    <Compile Include="LAB\LAB_Details.cs" />
    <Compile Include="MMCV_Function.cs" />
    <Compile Include="MMCV_Permission.cs" />
    <Compile Include="MMCV_TroubleIT.cs" />
    <Compile Include="MMCV_USER.cs" />
    <Compile Include="Notification\NotificationDAO.cs" />
    <Compile Include="OBA\AcceptableQualityLevel.cs" />
    <Compile Include="OBA\OBA_CosmeticInspection.cs" />
    <Compile Include="OBA\OBA_Functions.cs" />
    <Compile Include="OBA\OBA_LotShipment.cs" />
    <Compile Include="OBA\OBA_Machine.cs" />
    <Compile Include="OBA\OBA_MasterItems.cs" />
    <Compile Include="OBA\OBA_MasterRequest.cs" />
    <Compile Include="OBA\OBA_Packages.cs" />
    <Compile Include="OBA\OBA_PcsBarcodeGrade.cs" />
    <Compile Include="OBA\OBA_Result.cs" />
    <Compile Include="OBA\ProviderOBA.cs" />
    <Compile Include="ORT\ORT_Detail.cs" />
    <Compile Include="ORT\ORT_Master.cs" />
    <Compile Include="ORT\ORT_Result.cs" />
    <Compile Include="ORT\ORT_Update.cs" />
    <Compile Include="ORT\ProviderORT.cs" />
    <Compile Include="PIMV\IQC_Result.cs" />
    <Compile Include="PIMV\LAB_Details.cs" />
    <Compile Include="PIMV\LAB_MasterItems.cs" />
    <Compile Include="PIMV\LAB_MasterRequest.cs" />
    <Compile Include="PIMV\Mail_Settings.cs" />
    <Compile Include="PIMV\MMCV_Areas.cs" />
    <Compile Include="PIMV\MMCV_Employees.cs" />
    <Compile Include="PIMV\MMCV_Lines.cs" />
    <Compile Include="PIMV\MMCV_Models.cs" />
    <Compile Include="PIMV\MMCV_SamplePcsBarcode.cs" />
    <Compile Include="PIMV\OK2Ship_Details.cs" />
    <Compile Include="PIMV\OK2Ship_Master.cs" />
    <Compile Include="PIMV\OK2Ship_Results.cs" />
    <Compile Include="PIMV\PVS_Censorship.cs" />
    <Compile Include="PIMV\ToolJig_Details.cs" />
    <Compile Include="PIMV\ToolJig_Devices.cs" />
    <Compile Include="PIMV\ToolJig_MPN.cs" />
    <Compile Include="PIMV\ToolJig_TimesPunch.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QCI\QCI_Provider.cs" />
    <Compile Include="ModelsProvider.cs" />
    <Compile Include="Record\BoardManage.cs" />
    <Compile Include="SERVER.cs" />
    <Compile Include="SHIP\SHIP_Provider.cs" />
    <Compile Include="UserProvider.cs" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="VBIDE">
      <Guid>{0002E157-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>5</VersionMajor>
      <VersionMinor>3</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>