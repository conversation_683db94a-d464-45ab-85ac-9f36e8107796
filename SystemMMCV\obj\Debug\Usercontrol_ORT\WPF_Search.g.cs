﻿#pragma checksum "..\..\..\Usercontrol_ORT\WPF_Search.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "64542028424CA313F2091017B28C77EA213D47D23F906FC7C26F3654604EAF08"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.Usercontrol_ORT;


namespace SystemMMCV.Usercontrol_ORT {
    
    
    /// <summary>
    /// WPF_Search
    /// </summary>
    public partial class WPF_Search : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 48 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox tbx_model;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_mpn;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_lotno;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpk_date;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpk_dateEnd;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_delete;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_serialID;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_mpn;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_lotno;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_testdate;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_shift;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_spec;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_model;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_itemName;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker edt_prodate;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_line;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_frequency;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox edt_quantity;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_result;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_detail;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem edt_exit;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_total;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_ok;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_ng;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_rate;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_ort/wpf_search.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.tbx_model = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.tbx_mpn = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.tbx_lotno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.dpk_date = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.dpk_dateEnd = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            
            #line 53 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Search);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btn_delete = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
            this.btn_delete.Click += new System.Windows.RoutedEventHandler(this.Event_Delete);
            
            #line default
            #line hidden
            return;
            case 8:
            this.edt_serialID = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.edt_mpn = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.edt_lotno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.edt_testdate = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.edt_shift = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.edt_spec = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.edt_model = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.edt_itemName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.edt_prodate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 17:
            this.edt_line = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.edt_frequency = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.edt_quantity = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.dtg_result = ((System.Windows.Controls.DataGrid)(target));
            
            #line 101 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
            this.dtg_result.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Event_GetDetail);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 104 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_EditResuls);
            
            #line default
            #line hidden
            return;
            case 22:
            this.dtg_detail = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 23:
            
            #line 133 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_OpenFile);
            
            #line default
            #line hidden
            return;
            case 24:
            this.edt_exit = ((System.Windows.Controls.MenuItem)(target));
            
            #line 134 "..\..\..\Usercontrol_ORT\WPF_Search.xaml"
            this.edt_exit.Click += new System.Windows.RoutedEventHandler(this.Event_EditDetail);
            
            #line default
            #line hidden
            return;
            case 25:
            this.tbx_total = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.tbx_ok = ((System.Windows.Controls.TextBox)(target));
            return;
            case 27:
            this.tbx_ng = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.tbx_rate = ((System.Windows.Controls.TextBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

