﻿using System;
using System.Data;

namespace Models.DAO_LAB
{
    public class OK2Ship_Weeks
    {
		private int? serialID;
		private int? year;
		private string weekName;
		private DateTime? dateStart;
		private DateTime? dateEnd;
		private string requestNo;

        public int? SerialID { get => serialID; set => serialID = value; }
        public int? Year { get => year; set => year = value; }
        public string WeekName { get => weekName; set => weekName = value; }
        public DateTime? DateStart { get => dateStart; set => dateStart = value; }
        public DateTime? DateEnd { get => dateEnd; set => dateEnd = value; }
        public string RequestNo { get => requestNo; set => requestNo = value; }

        public OK2Ship_Weeks() { }
        public OK2Ship_Weeks(DataRow row)
        {
            this.SerialID = (int)row["SerialID"];
            this.Year = (int)row["Year"];
            this.WeekName = row["WeekName"].ToString();
            this.DateStart = (DateTime)row["DateStart"];
            this.DateEnd = (DateTime)row["DateEnd"];
            this.RequestNo = row["RequestNo"].ToString();
        }
    }
}
