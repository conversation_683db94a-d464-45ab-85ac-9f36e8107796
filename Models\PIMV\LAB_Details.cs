﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class LAB_Details
    {
        private long serialID;
        private string requestNo;
        private int index;
        private string items;
        private int quantity;
        private int errorLimits;
        private int errorQuantity;
        private string criteria;
        private string notes;
        private string performed;
        private int listTime;
        private DateTime? timeConfirm;
        private string userConfirm;
        private DateTime? timeFinish;
        private string userFinish;
        private string itemStatus;
        private string result;

        //private bool isOverTime;
        public long SerialID { get => serialID; set => serialID = value; }
        public string RequestNo { get => requestNo; set => requestNo = value; }
        public int Index { get => index; set => index = value; }
        public string Items { get => items; set => items = value; }
        public int Quantity { get => quantity; set => quantity = value; }
        public int Error_Limits { get => errorLimits; set => errorLimits = value; }
        public int Error_Quanty { get => errorQuantity; set => errorQuantity = value; }
        public string Criteria { get => criteria; set => criteria = value; }
        public string Notes { get => notes; set => notes = value; }
        public string Performed { get => performed; set => performed = value; }
        public int ListTime { get => listTime; set => listTime = value; }
        public DateTime? TimeConfirm { get => timeConfirm; set => timeConfirm = value; }
        public string UserConfirm { get => userConfirm; set => userConfirm = value; }
        public DateTime? TimeFinish { get => timeFinish; set => timeFinish = value; }
        public string UserFinish { get => userFinish; set => userFinish = value; }
        public string ItemStatus { get => itemStatus; set => itemStatus = value; }
        public string Result { get => result; set => result = value; }
        public bool IsOverTime
        {
            get
            {
                if (TimeFinish == null && TimeConfirm != null)
                {
                    var date = ((DateTime)TimeConfirm).AddHours(ListTime);
                    return date >= DateTime.Now;
                }
                else
                {
                    return true;
                }

            }
            private set => IsOverTime = value;
        }

        public LAB_Details() { }
        public LAB_Details(DataRow row)
        {
            this.SerialID = (long)row["SerialID"];
            this.RequestNo = row["RequestNo"].ToString();
            this.Index = (int)row["Index"];
            this.Items = row["Items"].ToString();
            this.Quantity = (int)row["Quantity"];
            this.Error_Limits = (int)row["ErrorLimits"];
            this.Error_Quanty = (int)row["ErrorQuantity"];
            this.Criteria = row["Criteria"].ToString();
            this.Notes = row["Notes"].ToString();
            this.Performed = row["Performed"].ToString();
            this.ListTime = (int)row["ListTime"];
            if (string.IsNullOrEmpty(row["TimeConfirm"].ToString())) this.TimeConfirm = null;
            else this.TimeConfirm = (DateTime?)row["TimeConfirm"];
            this.UserConfirm = row["UserConfirm"].ToString();

            if (string.IsNullOrEmpty(row["TimeFinish"].ToString())) this.TimeFinish = null;
            else this.TimeFinish = (DateTime?)row["TimeFinish"];

            this.UserFinish = row["UserFinish"].ToString();
            this.ItemStatus = row["ItemStatus"].ToString();
            this.Result = row["Result"].ToString();

        }
    }

    public class LAB_Details_EXC
    {
        public static DataTable GET_Details(out string exception, string requestno)
        {
            string query = $"Select * from LAB_Details With(NoLock) Where RequestNo = '{requestno}';";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV,query);
            return data;
        }
        public static DataRow Get_DetailsBySerialId(out string exception, long serialId)
        {
            string query = $"SELECT * From [LAB_Details] Where SerialID = {serialId}";
            var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
            if (string.IsNullOrEmpty(exception))
            {
                if (data != null && data.Rows.Count > 0) return data.Rows[0];
                else return null;
            }
            else
            {
                return null;
            }
        }
        public static DataTable Get_DetailsByLotno(out string exception,string lotno)
        {
            if (string.IsNullOrEmpty(lotno))
            {
                exception = "Không lấy được thông tin details do Lotno không hợp lệ.";
                return null;
            }
            else
            {
                string query = "SELECT A.[SerialID],[Index],[Items],[Performed],[ItemStatus],A.[Result] " +
                "FROM [PIMV].[dbo].[LAB_Details] A INNER JOIN LAB_Results B " +
                $"ON A.RequestNo = B.RequestNo Where [ItemStatus] = 'Finish' And Lotno = '{lotno}';";
                var data = ModelsProvider.SqlInstance.ExecuteQuery(out exception, SqlProvider.SqlSV.OFC68_PIMV, query);
                if (string.IsNullOrEmpty(exception))
                {
                    if (data != null) return data;
                    else return null;
                }
                else
                {
                    return null;
                }
            }
        }
    }

}
