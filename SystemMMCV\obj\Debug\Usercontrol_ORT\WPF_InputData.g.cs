﻿#pragma checksum "..\..\..\Usercontrol_ORT\WPF_InputData.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "E8FEDD422AA584B70CF3AE393D04380C8610015C5B5912064867EBCE810AD6B4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.Usercontrol_ORT;


namespace SystemMMCV.Usercontrol_ORT {
    
    
    /// <summary>
    /// WPF_InputData
    /// </summary>
    public partial class WPF_InputData : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 41 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbx_model;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_mpn;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_lotno;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dtp_prodate;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dtp_testdate;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox tbx_line;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox tbx_shift;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox tbx_frequency;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbx_stages;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox tbx_staffno;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel spn_content;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_ort/wpf_inputdata.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.cbx_model = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.tbx_mpn = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.tbx_lotno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.dtp_prodate = ((System.Windows.Controls.DatePicker)(target));
            
            #line 44 "..\..\..\Usercontrol_ORT\WPF_InputData.xaml"
            this.dtp_prodate.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.Get_TestDate);
            
            #line default
            #line hidden
            return;
            case 5:
            this.dtp_testdate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.tbx_line = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.tbx_shift = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.tbx_frequency = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.cbx_stages = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.tbx_staffno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.spn_content = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

