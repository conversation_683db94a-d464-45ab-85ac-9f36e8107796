﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.LAB
{
    public class LAB_Details_EXC
    {
        public static DataTable Get_TableDetail(out string exception,string _requestno)
        {
            string query = "Select * From [LAB_Details] With(NoLock) Where [RequestNo] = '" + _requestno + "'";
            return SERVER_PV.Instance.ExcuteQuery(out exception,SERVER_DF.Instance.SV68_PIMV, query);
        }

        public static DataTable Get_TableDetailsFinish(out string exception, string _requestno)
        {
            string query_result = $"Select [Status] from LAB_Results where RequestNo = '{_requestno}';";
            var status = SERVER_PV.Instance.ExcuteScalar(out exception, SERVER_DF.Instance.SV68_PIMV, query_result);
            if (string.IsNullOrEmpty(exception))
            {
                if (status != null && status.ToString() == "Complete")
                {
                    string query = "Select * From [LAB_Details] With(NoLock) Where [RequestNo] = '" + _requestno + "' And [ItemStatus] = 'Finish' order by [Index]";
                    return SERVER_PV.Instance.ExcuteQuery(out exception, SERVER_DF.Instance.SV68_PIMV, query);
                }
                else
                {
                    exception = "Yêu cầu cần thực hiện upload kết quả và xác nhận Censorship.";
                    return null;
                }
            }
            else
            {
                return null;
            }
        }
    }
}
