﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.Employees
{
    public class EmployeesDAO
    {
        public DataTable EmployeeInfo(string username)
        {
            string query = $"Select * FROM [EZ_MEKTEC].[dbo].[tbHR_Employee] where EmployeeCode = '{username}';";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.OFC91_EZ_MEKTEC, query);
            return data;
        }
        public DataTable Table_Employees(string compare = null, object[] parameter = null)
        {
            if (!string.IsNullOrEmpty(compare) && parameter != null)
            {
                string query = $"Select * From MMCV_Employees With(NoLock) Where {compare} order by SerialID";
                var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
                return data;
            }
            else
            {
                string query = "Select * From MMCV_Employees With(NoLock) order by SerialID";
                var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
                return data;
            }
        }

        public bool Employees_Add(string username, string password, string group, string department, string fullname)
        {
            string query = "Insert Into MMCV_USER ([Username],[Password],[IsAdmin],[Status],[GroupName],[Department],[FullName]) Values( @Username , @Password , @IsAdmin , @Status , @Group , @Department , @Fullname )";
            object[] parameter = new object[] { username, password, 0, 1, group, department, fullname };
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }
    }
}
