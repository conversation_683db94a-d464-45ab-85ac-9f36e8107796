﻿#pragma checksum "..\..\..\View\SoftwareMMCV.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "D2BA91E3D922712A8B588AB3128FBD59B2A7EA2035C143AF8A5FF3FAEBE4095C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.View;
using WpfAnimatedGif;


namespace SystemMMCV.View {
    
    
    /// <summary>
    /// SoftwareMMCV
    /// </summary>
    public partial class SoftwareMMCV : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 12 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DrawerHost mdh_menu;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel spn_menu;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox lbx_ItemQuanlity;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox lbx_ItemProduct;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox lbx_ItemIT;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox lbx_ItemOther;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.ColorZone clz_header;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txt_header;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Snackbar SnackbarInfo;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem mni_user;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem txt_username;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.MenuItem IsAdmin;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_shownotify;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_showmessage;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\View\SoftwareMMCV.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card scv_body;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/view/softwaremmcv.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\View\SoftwareMMCV.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.mdh_menu = ((MaterialDesignThemes.Wpf.DrawerHost)(target));
            return;
            case 2:
            
            #line 31 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Envet_ShowHome);
            
            #line default
            #line hidden
            return;
            case 3:
            this.spn_menu = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            
            #line 65 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.Expander)(target)).Expanded += new System.Windows.RoutedEventHandler(this.Expander_Expanded);
            
            #line default
            #line hidden
            return;
            case 5:
            this.lbx_ItemQuanlity = ((System.Windows.Controls.ListBox)(target));
            return;
            case 6:
            
            #line 77 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.Expander)(target)).Expanded += new System.Windows.RoutedEventHandler(this.Expander_Expanded);
            
            #line default
            #line hidden
            return;
            case 7:
            this.lbx_ItemProduct = ((System.Windows.Controls.ListBox)(target));
            return;
            case 8:
            
            #line 91 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.Expander)(target)).Expanded += new System.Windows.RoutedEventHandler(this.Expander_Expanded);
            
            #line default
            #line hidden
            return;
            case 9:
            this.lbx_ItemIT = ((System.Windows.Controls.ListBox)(target));
            return;
            case 10:
            
            #line 104 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.Expander)(target)).Expanded += new System.Windows.RoutedEventHandler(this.Expander_Expanded);
            
            #line default
            #line hidden
            return;
            case 11:
            this.lbx_ItemOther = ((System.Windows.Controls.ListBox)(target));
            return;
            case 12:
            this.clz_header = ((MaterialDesignThemes.Wpf.ColorZone)(target));
            return;
            case 13:
            this.txt_header = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.SnackbarInfo = ((MaterialDesignThemes.Wpf.Snackbar)(target));
            return;
            case 15:
            this.mni_user = ((System.Windows.Controls.MenuItem)(target));
            return;
            case 16:
            this.txt_username = ((System.Windows.Controls.MenuItem)(target));
            
            #line 161 "..\..\..\View\SoftwareMMCV.xaml"
            this.txt_username.Click += new System.Windows.RoutedEventHandler(this.Event_ShowProfile);
            
            #line default
            #line hidden
            return;
            case 17:
            this.IsAdmin = ((System.Windows.Controls.MenuItem)(target));
            
            #line 162 "..\..\..\View\SoftwareMMCV.xaml"
            this.IsAdmin.Click += new System.Windows.RoutedEventHandler(this.Event_ShowAdministrator);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 163 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_ShowChagePassword);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 164 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Logout);
            
            #line default
            #line hidden
            return;
            case 20:
            this.btn_shownotify = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\View\SoftwareMMCV.xaml"
            this.btn_shownotify.Click += new System.Windows.RoutedEventHandler(this.Event_ShowSnacbar);
            
            #line default
            #line hidden
            return;
            case 21:
            this.btn_showmessage = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\View\SoftwareMMCV.xaml"
            this.btn_showmessage.Click += new System.Windows.RoutedEventHandler(this.Event_ShowSnacbar);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 180 "..\..\..\View\SoftwareMMCV.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_ShowMenuUser);
            
            #line default
            #line hidden
            return;
            case 23:
            this.scv_body = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

