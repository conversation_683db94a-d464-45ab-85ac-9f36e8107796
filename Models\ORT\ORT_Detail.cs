﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.ORT
{
    public class ORT_Detail
    {
        private long noID;
        private long serialID;
        private string nameStage;
        private string typeDeadline;
        private DateTime? dateTest;
        private DateTime deadline;
        private string statusTest;
        private string link;

        public long NoID { get => noID; set => noID = value; }
        public long SerialID { get => serialID; set => serialID = value; }
        public string NameStage { get => nameStage; set => nameStage = value; }
        public string TypeDeadline { get => typeDeadline; set => typeDeadline = value; }
        public DateTime? DateTest { get => dateTest; set => dateTest = value; }
        public DateTime Deadline { get => deadline; set => deadline = value; }
        public string StatusTest { get => statusTest; set => statusTest = value; }
        public string Link { get => link; set => link = value; }


        public ORT_Detail(DataRow row)
        {
            this.NoID = (long)row["NoID"];
            this.SerialID = (long)row["SerialID"];
            this.NameStage = row["NameStage"].ToString();
            this.TypeDeadline = row["TypeDeadline"].ToString();
            if (string.IsNullOrEmpty(row["DateTest"].ToString()))
            {
                this.DateTest = null;
            }
            else this.DateTest = (DateTime)row["DateTest"];
            this.Deadline = (DateTime)row["Deadline"];
            this.StatusTest = row["StatusTest"].ToString();
            this.Link = row["Link"].ToString();
        }
    }
}
