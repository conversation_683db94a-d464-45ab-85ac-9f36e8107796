﻿

namespace Models.OBA
{
    public class OBA_CosmeticInspection
    {
		private long serialID;
		private string shipmentID;// nvarchar(20) not null,
		private string typeCheck; //varchar(50) not null,
		private string microscope;// nvarchar(20) NULL,
		private string mirrorTray;// nvarchar(20) NULL,
		private string aQL;// nvarchar(10) NULL,
		private int sampleQuantity;// int NULL,
		private int nG_Allowed;// int NULL,
		private int nG_Actual;// int NULL,
		private string nG_Content;// nvarchar(200) NULL,
		private string serious;// varchar(5) NULL,
		private string pathEvidence;// nvarchar(500) NULL,	
		private string judge;// varchar(5) NULL,

        public long SerialID { get => serialID; set => serialID = value; }
        public string ShipmentID { get => shipmentID; set => shipmentID = value; }
        public string TypeCheck { get => typeCheck; set => typeCheck = value; }
        public string Microscope { get => microscope; set => microscope = value; }
        public string MirrorTray { get => mirrorTray; set => mirrorTray = value; }
        public string AQL { get => aQL; set => aQL = value; }
        public int SampleQuantity { get => sampleQuantity; set => sampleQuantity = value; }
        public int NG_Allowed { get => nG_Allowed; set => nG_Allowed = value; }
        public int NG_Actual { get => nG_Actual; set => nG_Actual = value; }
        public string NG_Content { get => nG_Content; set => nG_Content = value; }
        public string Serious { get => serious; set => serious = value; }
        public string PathEvidence { get => pathEvidence; set => pathEvidence = value; }
        public string Judge { get => judge; set => judge = value; }
    }
}
