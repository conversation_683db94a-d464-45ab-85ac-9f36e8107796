﻿#pragma checksum "..\..\..\UserControl_IQC\IQC_Censorship.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "38F290997F6517F3D029B83DA08DE8F7C8207CB5E52EEB4D40452F9385E85F7A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_IQC;


namespace SystemMMCV.UserControl_IQC {
    
    
    /// <summary>
    /// IQC_Censorship
    /// </summary>
    public partial class IQC_Censorship : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 24 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_requestno;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_mpn;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_lotno;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_username;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_censorshipp;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost dlh_note;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_note;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_labDetail;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_iqc/iqc_censorship.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txt_requestno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.txt_mpn = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.txt_lotno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txt_username = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            
            #line 28 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Search);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 29 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Refresh);
            
            #line default
            #line hidden
            return;
            case 7:
            this.dtg_censorshipp = ((System.Windows.Controls.DataGrid)(target));
            
            #line 51 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            this.dtg_censorshipp.PreviewMouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.Event_LoadDetail);
            
            #line default
            #line hidden
            return;
            case 10:
            this.dlh_note = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 11:
            this.txt_note = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            
            #line 204 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_SubmitNote);
            
            #line default
            #line hidden
            return;
            case 13:
            this.dtg_labDetail = ((System.Windows.Controls.DataGrid)(target));
            
            #line 210 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            this.dtg_labDetail.AutoGeneratedColumns += new System.EventHandler(this.dtg_labDetail_AutoGeneratedColumns);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 8:
            
            #line 57 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Censorship);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 58 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Censorship);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 216 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btn_view_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 217 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_ReturnCensorship);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 218 "..\..\..\UserControl_IQC\IQC_Censorship.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Note);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

