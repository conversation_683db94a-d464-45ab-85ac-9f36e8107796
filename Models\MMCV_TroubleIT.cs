﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models
{
    public class MMCV_TroubleIT
    {
        private long serialID;
        private DateTime startTime;
        private DateTime endTime;
        private string fixTime;
        private string groupIT;
        private string troubleDetail;
        private string errorType;
        private string errorBy;
        private string errorFrequency;
        private string area;
        private string process;
        private string line;
        private string device;
        private string driSection;
        private string itPic;
        private string level;
        private string status;
        private string relate;
        private string reason;
        private string action;
        private string nextAction;
        private string staffNo;
        private DateTime enterTime;
        private string deletedBy;
        private bool deleted;
        public long SerialID { get => serialID; set => serialID = value; }
        public DateTime StartTime { get => startTime; set => startTime = value; }
        public DateTime EndTime { get => endTime; set => endTime = value; }
        public string GroupIT { get => groupIT; set => groupIT = value; }
        public string TroubleDetail { get => troubleDetail; set => troubleDetail = value; }
        public string ErrorType { get => errorType; set => errorType = value; }
        public string Area { get => area; set => area = value; }
        public string Process { get => process; set => process = value; }
        public string Line { get => line; set => line = value; }
        public string Device { get => device; set => device = value; }
        public string DriSection { get => driSection; set => driSection = value; }
        public string ItPic { get => itPic; set => itPic = value; }
        public string Level { get => level; set => level = value; }
        public string Status { get => status; set => status = value; }
        public string Reason { get => reason; set => reason = value; }
        public string Action { get => action; set => action = value; }
        public DateTime EnterTime { get => enterTime; set => enterTime = value; }
        public string NextAction { get => nextAction; set => nextAction = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public string DeletedBy { get => deletedBy; set => deletedBy = value; }
        public bool Deleted { get => deleted; set => deleted = value; }
        public string FixTime { get => fixTime; set => fixTime = value; }
        public string ErrorBy { get => errorBy; set => errorBy = value; }
        public string ErrorFrequency { get => errorFrequency; set => errorFrequency = value; }
        public string Relate { get => relate; set => relate = value; }

        public MMCV_TroubleIT(DataRow row)
        {
            this.SerialID = (long)row["SerialID"];
            this.StartTime = (DateTime)row["StartTime"];
            this.EndTime = (DateTime)row["EndTime"];
            this.GroupIT = row["GroupIT"].ToString();
            this.TroubleDetail = row["TroubleDetail"].ToString();
            this.ErrorType = row["ErrorType"].ToString();
            this.ErrorBy = row["ErrorBy"].ToString();
            this.ErrorFrequency = row["ErrorFrequency"].ToString();
            this.Area = row["Area"].ToString();
            this.Process = row["Process"].ToString();
            this.Line = row["Line"].ToString();
            this.Device = row["Device"].ToString();
            this.DriSection = row["DriSection"].ToString();
            this.ItPic = row["ItPic"].ToString();
            this.Level = row["Level"].ToString();
            this.Status = row["Status"].ToString();
            this.Relate = row["Relate"].ToString();
            this.Reason = row["Reason"].ToString();
            this.Action = row["Action"].ToString();
            this.NextAction = row["NextAction"].ToString();
            this.staffNo = row["StaffNo"].ToString();
            this.EnterTime = (DateTime)row["EnterTime"];
            this.DeletedBy = row["DeletedBy"].ToString();
            this.Deleted = (bool)row["Deleted"];
            this.FixTime = (this.EndTime - this.StartTime).ToString();
        }
    }
}
