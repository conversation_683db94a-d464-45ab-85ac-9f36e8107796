﻿#pragma checksum "..\..\..\UserControl_JIG\SupportBlock.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "90F55F18C30A74476772C9ECC5F910D337F7091ED66ADA23AE8AD45DCF17B5CA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_JIG;


namespace SystemMMCV.UserControl_JIG {
    
    
    /// <summary>
    /// SupportBlock
    /// </summary>
    public partial class SupportBlock : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 17 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DrawerHost drh_main;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_barcode;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_boardType;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_maintainFrequency;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_afterMaintainUseTimes;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_scrapLimitTimes;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_historyUseTimes;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_state;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_directory;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_line;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cbb_area;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox search_JigBarcode;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\UserControl_JIG\SupportBlock.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dtg_data;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_jig/supportblock.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_JIG\SupportBlock.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.drh_main = ((MaterialDesignThemes.Wpf.DrawerHost)(target));
            return;
            case 2:
            this.txt_barcode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.txt_boardType = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txt_maintainFrequency = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.txt_afterMaintainUseTimes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.txt_scrapLimitTimes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.txt_historyUseTimes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txt_state = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.txt_directory = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.txt_line = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.cbb_area = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            
            #line 52 "..\..\..\UserControl_JIG\SupportBlock.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Submit);
            
            #line default
            #line hidden
            return;
            case 13:
            this.search_JigBarcode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            
            #line 68 "..\..\..\UserControl_JIG\SupportBlock.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_SearchSupportBlock);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 69 "..\..\..\UserControl_JIG\SupportBlock.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_AddSupportBlock);
            
            #line default
            #line hidden
            return;
            case 16:
            this.dtg_data = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 17:
            
            #line 79 "..\..\..\UserControl_JIG\SupportBlock.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_TimesReset);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

