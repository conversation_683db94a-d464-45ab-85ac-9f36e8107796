﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class LAB_MasterItems
    {

    }

    /// <summary>
    /// L<PERSON>y danh sách model(duy nhất) trong LAB_MasterItems_EXC
    /// </summary>
    public class LAB_MasterItems_EXC
    {
        public static DataTable GET_Models(out string exception)
        {            
            try
            {
                string query = "SELECT Distinct(Model) FROM [LAB_MasterItems] with(nolock) Order by Model";
                return ModelsProvider.SqlInstance.ExecuteQuery(out exception,SqlProvider.SqlSV.OFC68_PIMV,query,null);                
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return null;
            }           
        }
    }
}
