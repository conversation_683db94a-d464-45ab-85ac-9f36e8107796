﻿

namespace Models.OBA
{
    public class OBA_Packages
    {
		private long serialID;// bigint identity(1,1),
		private string shipmentID;// nvarchar(20) NOT NULL,
		private string typeCheck;// varchar(50) NOT NULL,
		private string cartonBoxID;// varchar(50) NULL,
		private string albagID;// varchar(50) NULL,
		private int sampleQuantity;// int NULL,
		private string pathEvidence;// nvarchar(500) NULL,	
		private string judge;// varchar(5) NULL,

        public long SerialID { get => serialID; set => serialID = value; }
        public string ShipmentID { get => shipmentID; set => shipmentID = value; }
        public string TypeCheck { get => typeCheck; set => typeCheck = value; }
        public string CartonBoxID { get => cartonBoxID; set => cartonBoxID = value; }
        public string AlbagID { get => albagID; set => albagID = value; }
        public int SampleQuantity { get => sampleQuantity; set => sampleQuantity = value; }
        public string PathEvidence { get => pathEvidence; set => pathEvidence = value; }
        public string Judge { get => judge; set => judge = value; }
    }
}
