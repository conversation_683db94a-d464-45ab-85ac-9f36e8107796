﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    public class ToolJig_Details
    {
    }

    public class ToolJig_Details_EXC
    {
        public static DataTable JIG_Detail(out string exception ,int JIG_ID)
        {
            string query = "Select * From ToolJig_Details Where [ToolJigID] = " + JIG_ID;
            return SERVER_PV.Instance.ExcuteQuery(out exception,SERVER_DF.Instance.SV48_PIMV, query);
        }
    }
}
