﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.IQC
{
    public class IQC_Provider
    {
        public DataTable AQL_Table()
        {
            string query = "Select Distinct(AQL) From [AcceptableQualityLevel]";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }

        #region IQC_TypeMaster
        public DataTable IQC_TypeMaster_Table()
        {
            string query = "Select * From [IQC_TypeMaster]";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }
        public bool IQC_TypeMaster_Add(string _typename, string _username, string _columns)
        {
            var parameter = new object[] { _typename, _username, _columns };
            string query = $"Insert Into [IQC_TypeMaster]([TypeName],[UserUpdate],[ColumnsList],[EnterTime]) Values( @TypeName , @UserUpdate , @ColumnsList , GetDate() )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public static bool IQC_TypeMaster_Edit(int _typeId, string _username, string _columns)
        {
            var parameter = new object[] { _username, _columns, _typeId };
            string query = $"Update [IQC_TypeMaster] Set [UserUpdate] = @UserUpdate ,[ColumnsList] = @ColumnsList ,[EnterTime] = GetDate() Where [TypeID] = @TypeId ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool IQC_TypeMaster_Delete(int _id)
        {
            string query = $"Delete From [IQC_TypeMaster] Where [TypeID] = {_id}";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        public string TypeMaster_ColumnsList(int typeid)
        {
            string query = $"Select [ColumnsList] From [IQC_TypeMaster] Where [TypeID] = {typeid} ";
            var res = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res.ToString();
        }
        #endregion

        #region IQC_MasterItem

        public static DataTable IQC_MasterItem_Table(string typename = null)
        {
            string query;
            object[] parameter = null;
            if (string.IsNullOrEmpty(typename))
            {
                query = "Select * From [IQC_MasterItem] Order by [TypeID],[ItemIndex]";
            }
            else
            {
                query = "Select a.* From [IQC_MasterItem] as a Inner join [IQC_TypeMaster] as b On a.TypeID = b.TypeID Where b.TypeName = @TypeName Order by [TypeID],[ItemIndex]";
                parameter = new object[] { typename };
            }
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return data;
        }

        public DataTable IQC_MasterItem_Table(int typeid)
        {
            string query = $"Select * From [IQC_MasterItem] Where [TypeID] = {typeid} Order by [TypeID],[ItemIndex] ";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }
        public bool IQC_MasterItem_Add(int TypeID, int ItemIndex, string ItemName, string AQL, string NPI, double QtyNPI, string MP, double QtyMP, string UserUpdate)
        {
            var parameter = new object[] { TypeID, ItemIndex, ItemName, AQL, NPI, QtyNPI, MP, QtyMP, UserUpdate };
            string query = "Insert Into [IQC_MasterItem] ([TypeID],[ItemIndex],[ItemName],[AQL],[NPI],[NPI_Qty],[MP],[MP_Qty],[UserUpdate],[EnterTime]) " +
                "Values ( @TypeID , @ItemIndex , @ItemName , @AQL , @NPI , @QtyNPI , @MP , @QtyMP , @UserUpdate , GetDate() )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool IQC_MasterItem_Edit(int TypeID, int ItemIndex, string ItemName, string AQL, string NPI, double QtyNPI, string MP, double QtyMP, string UserUpdate, int SerialID)
        {
            var parameter = new object[] { TypeID, ItemIndex, ItemName, AQL, NPI, QtyNPI, MP, QtyMP, UserUpdate, SerialID };
            string query = "Update [IQC_MasterItem] Set [TypeID] = @TypeID ,[ItemIndex] = @ItemIndex ,[ItemName] = @ItemName ,[AQL] = @AQL ,[NPI] = @NPI ,[NPI_Qty] = @NPI_Qty ,[MP] = @MP ,[MP_Qty] = @MP_Qty ,[UserUpdate] = @UserUpdate ,[EnterTime] = GetDate() Where [SerialID] = @SerialID ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public static bool IQC_MasterItem_Delete(int serialID)
        {
            string query = $"Delete From [IQC_MasterItem] Where [SerialID] = {serialID}";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }

        public static string GET_TypeName(int typeId)
        {
            string query = $"Select TypeName From [IQC_TypeMaster] Where TypeID = {typeId};";
            var value = DataProvider.Instance.ExecuteScalar(DataProvider.MMCV_Server.Server68_PIMV, query);
            return value.ToString();
        }
        public static string GET_Performed(string typename, string items)
        {
            if (string.IsNullOrEmpty(typename) || string.IsNullOrEmpty(items))
            {
                return null;
            }
            else
            {
                string query = $"SELECT TOP 1 * FROM [PIMV].[dbo].[LAB_MasterItems] WHere Model = N'{typename}' And Items = N'{items}';";
                var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
                if (data != null && data.Rows.Count == 1)
                {
                    return $"{data.Rows[0]["Performed"]};{data.Rows[0]["Listtime"]}";
                }
                else
                {

                    return null;
                }

            }
        }
        #endregion

        #region Supplier

        public DataTable Distinct_SupplierCode()
        {
            string query = "Select Distinct(SupplierCode) From IQC_Supplier";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }
        public DataTable Supplier_Table(string suppliercode = null)
        {
            string query = "Select * From IQC_Supplier";
            if (!string.IsNullOrEmpty(suppliercode))
            {
                query = $"Select * From IQC_Supplier Where [SupplierCode] Like '%{suppliercode}%';";
            }
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }

        public bool Supplier_Add(string supliercode, string suppliername, string note, string username)
        {
            var parameter = new object[] { supliercode, suppliername, note, username };
            string query = "Insert Into [IQC_Supplier] ([SupplierCode],[SupplierName],[Notes],[UserUpdate],[EnterTime]) Values ( @SupplierCode , @SupplierName , @Notes , @UserUpdate , GetDate() )";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Supplier_Edit(string supliercode, string suppliername, string note, string username, int serialid)
        {
            var parameter = new object[] { supliercode, suppliername, note, username, serialid };
            string query = "Update [IQC_Supplier] Set [SupplierCode] = @SupplierCode , [SupplierName] = @SupplierName ,[Notes] = @Notes ,[UserUpdate] = @UserUpdate ,[EnterTime] = GetDate() Where [SerialID] = @SerialId ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query, parameter);
            return res > 0;
        }

        public bool Supplier_Delete(int serialid)
        {
            string query = $"Delete From [IQC_Supplier] Where [SerialID] = {serialid} ";
            var res = DataProvider.Instance.ExecuteNonQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return res > 0;
        }
        #endregion

        #region Materials
        public DataTable Materials_Table(string suppliercode = null)
        {
            string query = "Select * From [IQC_Materials]";
            if (!string.IsNullOrEmpty(suppliercode))
            {
                query = $"Select * From [IQC_Materials] Where [SupplierCode] = '{suppliercode}';";
            }
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }       

        #endregion

        #region ResultTest
        public DataTable ResultTest_Table()
        {
            string query = "Select * From [IQC_Results] Where [MektecResult] = 'On-Going'";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }

        public DataTable Details_Table(long ID)
        {
            string query = $"Select * From [IQC_Details] Where [ID] = {ID};";
            var data = DataProvider.Instance.ExecuteQuery(DataProvider.MMCV_Server.Server68_PIMV, query);
            return data;
        }
        #endregion
    }
}
