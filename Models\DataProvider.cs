﻿using Models.Notification;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using System.Windows;

namespace Models
{
    public class DataProvider
    {
        private static DataProvider instance;
        public static DataProvider Instance { get { if (instance == null) instance = new DataProvider(); return instance; } private set => instance = value; }
        private DataProvider() { }

        #region MyConnectString
        public enum MMCV_Server
        {
            OFC91_EZ_MEKTEC,
            Server68_PIMV,
            Server45_PIMD,
            Server46_PIMD,
            Server48_PIMV,
            Server69_PIMD,
            Server46_Record,
            Server69_Record,
            NULL
        }
        string ConnectString(MMCV_Server SeverIndex)
        {
            string TextConnect = null;
            switch (SeverIndex)
            {
                case MMCV_Server.OFC91_EZ_MEKTEC:
                    TextConnect = @"Server = ********** ; database = EZ_MEKTEC; Uid = mmcv; Pwd = *********; MultipleActiveResultSets=True";
                    break;
                case MMCV_Server.Server68_PIMV:
                    TextConnect = @"Server = ********** ; database = PIMV; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case MMCV_Server.Server45_PIMD:
                    TextConnect = @"Server = ********** ; database = PIMD; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case MMCV_Server.Server46_PIMD:
                    TextConnect = @"Server = ********** ; database = PIMD; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case MMCV_Server.Server48_PIMV:
                    TextConnect = @"Server = ********** ; database = PIMV; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case MMCV_Server.Server69_PIMD:
                    TextConnect = @"Server = ********** ; database = PIMD; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case MMCV_Server.Server46_Record:
                    TextConnect = @"Server = ********** ; database = Record; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                case MMCV_Server.Server69_Record:
                    TextConnect = @"Server = ********** ; database = Record; Uid = pim; Pwd = *******; MultipleActiveResultSets=True";
                    break;
                default:
                    break;
            }
            return TextConnect;
        }
        #endregion

        #region MyExcuteSQL
        /// <summary>
        /// Try vấn dữ liệu
        /// </summary>
        /// <param name="sever"></param>
        /// <param name="query"></param>
        /// <param name="object parameter"></param>
        /// <returns>Thành công trả về khác null</returns>
        public DataTable ExecuteQuery(MMCV_Server sever, string query, object[] parameter = null)
        {
            DataTable data = new DataTable();
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                if (parameter[i] == null) 
                                {
                                    command.Parameters.AddWithValue(item, DBNull.Value);
                                }
                                else
                                {
                                    command.Parameters.AddWithValue(item, parameter[i]);
                                }                                
                                i++;
                            }
                        }
                    }
                    SqlDataAdapter adapter = new SqlDataAdapter(command);
                    adapter.Fill(data);
                    connection.Close();
                }
            }
            catch (Exception)
            {
                return null;
            }

            return data;
        }
        /// <summary>
        /// Cập nhật thay đổi dữ liệu
        /// </summary>
        /// <param name="sever"></param>
        /// <param name="query"></param>
        /// <param name="object parameter"></param>
        /// <returns>Thành công trả về khác 0</returns>
        public int ExecuteNonQuery(MMCV_Server sever, string query, object[] parameter = null)
        {
            int data = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                if(parameter[i] == null) command.Parameters.AddWithValue(item, DBNull.Value);
                                else command.Parameters.AddWithValue(item, parameter[i]);
                                i++;
                            }
                        }
                    }
                    data = command.ExecuteNonQuery();
                    connection.Close();
                }
            }
            catch 
            {                
                return data;
            }
            return data;
        }
        /// <summary>
        /// Truy vấn dữ liệu ô đầu tiên trong bảng
        /// </summary>
        /// <param name="sever"></param>
        /// <param name="query"></param>
        /// <param name="object parameter"></param>
        /// <returns>Thành công trả về khác null</returns>
        public object ExecuteScalar(MMCV_Server sever, string query, object[] parameter = null)
        {
            object data = null;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                command.Parameters.AddWithValue(item, parameter[i]);
                                i++;
                            }
                        }
                    }
                    data = command.ExecuteScalar();
                    connection.Close();
                }
            }
            catch (Exception)
            {
                return data;
            }
            return data;
        }
        /// <summary>
        /// Truy vấn thời gian hiện tại từ sever
        /// </summary>
        /// <param name="sever"></param>
        /// <returns>Thành công trả về giá trị khác DateTime.MinValue </returns>
        public DateTime? GetTimeSever(MMCV_Server sever)
        {
            try
            {
                DateTime data = DateTime.Parse(ExecuteScalar(sever, "select getdate()").ToString());
                return data;
            }
            catch (Exception)
            {
                return null;
            }
        }
        #endregion

        #region MyTransaction
        public int ExcuteNonTransaction(MMCV_Server sever, string query)
        {
            int data = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    SqlTransaction transaction = connection.BeginTransaction();
                    try
                    {
                        command.Transaction = transaction;
                        data = command.ExecuteNonQuery();
                        transaction.Commit();
                    }
                    catch (Exception ex )
                    {
                        Console.WriteLine(ex);
                        transaction.Rollback();
                    }
                    finally
                    {
                        transaction.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);       
             }
            return data;
        }
        public int ExcuteNonTransaction(MMCV_Server sever, string query, object[] parameter)
        {
            int data = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    SqlTransaction transaction = connection.BeginTransaction();
                    try
                    {
                        if (parameter != null)
                        {
                            string[] listPara = query.Split(' ');
                            int i = 0;
                            foreach (string item in listPara)
                            {
                                if (item.Contains('@'))
                                {
                                    if (parameter[i] == null)
                                    {
                                        command.Parameters.AddWithValue(item, DBNull.Value);
                                    }
                                    else command.Parameters.AddWithValue(item, parameter[i]);
                                    i++;
                                }
                            }
                        }
                        command.Transaction = transaction;
                        data = command.ExecuteNonQuery();
                        transaction.Commit();
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                    }
                    finally
                    {
                        transaction.Dispose();
                    }
                }
            }
            catch { }
            return data;
        }
        public object ExecuteIdentity(MMCV_Server sever, string query, object[] parameter = null)
        {
            object data = null;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                command.Parameters.AddWithValue(item, parameter[i]);
                                i++;
                            }
                        }
                    }
                    var res = command.ExecuteNonQuery();
                    if (res>0)
                    {                        
                        command.CommandText = "SELECT @@IDENTITY";
                        data = command.ExecuteScalar();
                    }
                    connection.Close();
                }
            }
            catch (Exception)
            {
                return data;
            }
            return data;
        }
        #endregion

        #region MyRegion
        /// <summary>
        /// Lấy id vừa tăng khi insert dữ liệu
        /// </summary>
        /// <param name="sever"></param>
        /// <param name="query"></param>
        /// <param name="parameter"></param>
        /// <returns></returns>
        public int ExcuteIdentity(MMCV_Server sever, string query, object[] parameter = null)
        {
            int _identity = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(ConnectString(sever)))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand(query, connection);
                    if (parameter != null)
                    {
                        string[] listPara = query.Split(' ');
                        int i = 0;
                        foreach (string item in listPara)
                        {
                            if (item.Contains('@'))
                            {
                                command.Parameters.AddWithValue(item, parameter[i]);
                                i++;
                            }
                        }
                    }
                    int data = command.ExecuteNonQuery();
                    if (data > 0)
                    {
                        command = new SqlCommand("Select @@IDENTITY as LastID", connection);
                        _identity = int.Parse(command.ExecuteScalar().ToString());
                    }
                    connection.Close();
                }
            }
            catch
            {
                //MessageBox.Show(ex.Message);
                return _identity;
            }
            return _identity;
        }
        #endregion
    }
}
