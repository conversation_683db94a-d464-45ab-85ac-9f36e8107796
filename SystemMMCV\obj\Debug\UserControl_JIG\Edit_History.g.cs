﻿#pragma checksum "..\..\..\UserControl_JIG\Edit_History.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "2793EE7A8BCCE47491253ACD574070341C89C761B2DDD85CA87F30163AF6204D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_JIG;


namespace SystemMMCV.UserControl_JIG {
    
    
    /// <summary>
    /// Edit_History
    /// </summary>
    public partial class Edit_History : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_serialID;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_timesReset;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker txt_startedDate;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker txt_mantenceDate;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_staffno;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_jigName;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_jigPunch;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_numday;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_limit;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_result;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\UserControl_JIG\Edit_History.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_evidence;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_jig/edit_history.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_JIG\Edit_History.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txt_serialID = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.txt_timesReset = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.txt_startedDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.txt_mantenceDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.txt_staffno = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.txt_jigName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.txt_jigPunch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txt_numday = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.txt_limit = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.txt_result = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.txt_evidence = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            
            #line 55 "..\..\..\UserControl_JIG\Edit_History.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_LoadEvidence);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 58 "..\..\..\UserControl_JIG\Edit_History.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Event_Submit);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

