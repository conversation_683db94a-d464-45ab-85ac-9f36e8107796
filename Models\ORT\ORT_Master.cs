﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.ORT
{
    public class ORT_Master
    {
        private int noID;
        private int itemIndex;
        private string itemName;
        private string spec;
        private int stageIndex;
        private string stageName;
        private string typeDeadline;
        private int numDay;
        private string folderLink;
        private int? quantity;

        public int NoID { get => noID; set => noID = value; }
        public int ItemIndex { get => itemIndex; set => itemIndex = value; }
        public string ItemName { get => itemName; set => itemName = value; }
        public string Spec { get => spec; set => spec = value; }
        public int StageIndex { get => stageIndex; set => stageIndex = value; }
        public string StageName { get => stageName; set => stageName = value; }
        public string TypeDeadline { get => typeDeadline; set => typeDeadline = value; }
        public int NumDay { get => numDay; set => numDay = value; }
        public string FolderLink { get => folderLink; set => folderLink = value; }
        public int? Quantity { get => quantity; set => quantity = value; }

        public ORT_Master(DataRow row)
        {
            this.NoID = (int)row["NoID"];
            this.ItemIndex = (int)row["ItemIndex"];
            this.ItemName = row["ItemName"].ToString();
            this.Spec = row["Spec"].ToString();
            this.StageIndex = (int)row["StageIndex"];
            this.StageName = row["StageName"].ToString();
            this.TypeDeadline = row["TypeDeadline"].ToString();
            this.NumDay = string.IsNullOrEmpty(row["NumDay"].ToString()) ? 0 : (int)row["NumDay"];
            this.FolderLink = row["FolderLink"].ToString();
            this.Quantity = string.IsNullOrEmpty(row["Quantity"].ToString()) ? 0 : (int)row["Quantity"];
        }
    }
}
