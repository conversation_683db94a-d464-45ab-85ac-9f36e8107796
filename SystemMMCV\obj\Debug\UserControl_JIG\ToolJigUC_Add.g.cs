﻿#pragma checksum "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "D38D85BB49C119A44377CC84F3BF53E929F90BB0086FDD95FC3CFFE47E8CAF2C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using SystemMMCV.UserControl_JIG;


namespace SystemMMCV.UserControl_JIG {
    
    
    /// <summary>
    /// ToolJigUC_Add
    /// </summary>
    public partial class ToolJigUC_Add : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txt_title;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel wpn_header;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_serialID;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_area;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_line;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_model;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_processID;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_nameJig;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_jigType;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_shtbarcode;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_partnumber;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox txt_type;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_punchLimit;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_warningpunch;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_numday;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_warningday;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_reportlink;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ckb_onlyonce;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txt_totalMachine;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btn_add;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SystemMMCV;component/usercontrol_jig/tooljiguc_add.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txt_title = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.wpn_header = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 3:
            this.txt_serialID = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txt_area = ((System.Windows.Controls.ComboBox)(target));
            
            #line 36 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
            this.txt_area.DropDownClosed += new System.EventHandler(this.Event_LoadLine);
            
            #line default
            #line hidden
            return;
            case 5:
            this.txt_line = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.txt_model = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.txt_processID = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txt_nameJig = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.txt_jigType = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.txt_shtbarcode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.txt_partnumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.txt_type = ((System.Windows.Controls.ComboBox)(target));
            
            #line 74 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
            this.txt_type.DropDownClosed += new System.EventHandler(this.Event_LoadType);
            
            #line default
            #line hidden
            return;
            case 13:
            this.txt_punchLimit = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.txt_warningpunch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.txt_numday = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.txt_warningday = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.txt_reportlink = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.ckb_onlyonce = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.txt_totalMachine = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.btn_add = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\UserControl_JIG\ToolJigUC_Add.xaml"
            this.btn_add.Click += new System.Windows.RoutedEventHandler(this.AddCommand_Executed);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

