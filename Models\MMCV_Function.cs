﻿using System;
using System.Data;

namespace Models
{
    public class MMCV_Function
    {
        private string functionID;
        private string functionName;
        private string groupName;
        private string describe;
        private string userCreate;
        private DateTime? enterTime;

        public string FunctionID { get => functionID; set => functionID = value; }
        public string FunctionName { get => functionName; set => functionName = value; }
        public string Describe { get => describe; set => describe = value; }
        public string UserCreate { get => userCreate; set => userCreate = value; }
        public DateTime? EnterTime { get => enterTime; set => enterTime = value; }
        public string GroupName { get => groupName; set => groupName = value; }

        public MMCV_Function() { }
        public MMCV_Function(DataRow row)
        {
            this.FunctionID = row["FunctionID"].ToString();
            this.FunctionName = row["FunctionName"].ToString();
            this.GroupName = row["GroupName"].ToString();
            this.Describe = row["Describe"].ToString();
            this.UserCreate = row["UserCreate"].ToString();
            if (string.IsNullOrEmpty(row["EnterTime"].ToString())) this.EnterTime = null;
            else this.EnterTime = (DateTime)row["EnterTime"];
        }
    }
}
