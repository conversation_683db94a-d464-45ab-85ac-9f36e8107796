﻿using System;

namespace Models.OBA
{
    public class OBA_LotShipment
    {
        private long serialID;
        private string shipmentID;
        private string lotno;
        private bool isLotOBA;
        private string invoice;
        private string staffNo;
        private DateTime enterTime;

        public long SerialID { get => serialID; set => serialID = value; }
        public string ShipmentID { get => shipmentID; set => shipmentID = value; }
        public string Lotno { get => lotno; set => lotno = value; }
        public bool IsLotOBA { get => isLotOBA; set => isLotOBA = value; }
        public string Invoice { get => invoice; set => invoice = value; }
        public string StaffNo { get => staffNo; set => staffNo = value; }
        public DateTime EnterTime { get => enterTime; set => enterTime = value; }
    }
}
