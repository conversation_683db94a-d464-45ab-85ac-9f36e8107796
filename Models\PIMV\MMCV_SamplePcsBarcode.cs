﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models.PIMV
{
    class MMCV_SamplePcsBarcode
    {
    }

    public class MMCV_SamplePcsBarcode_EXC
    {
        public static int? Count_SamplePcsBarcode(out string exception, string requestno)
        {
            var query = $"Select Count(*) from [MMCV_SamplePcsBarcode] Where [RequestNo] = '{requestno}';";
            var res = SERVER_PV.Instance.ExcuteScalar(out exception, SERVER_DF.Instance.SV68_PIMV, query);
            if (res!= null) return int.Parse(res.ToString());
            else return null;
        }
        public static bool Compare_SamplePcsBarcode(out string exception, string requestno, List<string> PcsBarcodes)
        {
            try
            {
                var query_1 = $"Select Count(*) from [MMCV_SamplePcsBarcode] Where [RequestNo] = '{requestno}';";

                string str_barcode = string.Join(",", PcsBarcodes.Select(item => $"'{item}'"));
                var query_2 = $"Select Count(*) from [MMCV_SamplePcsBarcode] Where [RequestNo] = '{requestno}' And [PcsBarcode] in ({str_barcode});";

                var data_1 = SERVER_PV.Instance.ExcuteScalar(out exception, SERVER_DF.Instance.SV68_PIMV, query_1);
                if (!string.IsNullOrEmpty(exception)) return false;
                var data_2 = SERVER_PV.Instance.ExcuteScalar(out exception, SERVER_DF.Instance.SV68_PIMV, query_2);
                if (!string.IsNullOrEmpty(exception)) return false;

                var count_1 = int.Parse(data_1.ToString());
                var count_2 = int.Parse(data_2.ToString());

                if (count_1 == count_2) return true;
                else return false;
            }
            catch (Exception ex)
            {
                exception = ex.Message;
                return false;
            }       
        }
    }
}
